<template>
    <div class="ad">
        <div class="title">
            {{ useUserStore().amm_vip ? state.link会员.恭喜您已获得 : state.link会员.Link着数会员MyGarden }}
        </div>
        <div class="desc">
            {{
                useUserStore().amm_vip
                    ? state.link会员.Link着数会员MyGarden权益
                    : state.link会员.开通Link着数会员即可享受以下权益
            }}
        </div>
        <div class="list">
            <div class="list-item">
                <div class="list-type">
                    <img :src="$imgs['dialog/linkMember-icon1.png']" />
                    <span>{{ state.link会员.种植积分 }}</span>
                </div>
                <p class="list-desc">{{ state.link会员.每个种植阶段积分奖励都加增 }}</p>
                <div class="list-box">
                    <div class="left">
                        <img :src="$imgs['dialog/linkMember-icon-points.png']" />
                        <span>{{ state.link会员.积分 }}</span>
                    </div>
                    <img class="up" :src="$imgs['dialog/linkMember-up.png']" />
                    <div class="right">
                        <div class="bg">
                            <span>{{ state.link会员.加赠了 }}</span>
                            <img :src="$imgs['dialog/linkMember-icon-50.png']" />
                        </div>
                    </div>
                </div>
            </div>
            <div class="list-item">
                <div class="list-type">
                    <img :src="$imgs['dialog/linkMember-icon1.png']" />
                    <span>{{ state.link会员.碳值卡 }}</span>
                </div>
                <!-- prettier-ignore -->
                <p class="list-desc" v-html="state.link会员.每月后付客户限定派发碳值升级"></p>
                <div class="list-box">
                    <div class="left">
                        <img :src="$imgs['dialog/linkMember-icon-10g.png']" />
                        <span>10g</span>
                    </div>
                    <img class="up" :src="$imgs['dialog/linkMember-up.png']" />
                    <div class="right">
                        <div class="bg">
                            <span>{{ state.link会员.升级至 }}</span>
                            <img :src="$imgs['dialog/linkMember-icon-30g.png']" />
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="tips" v-html="state.link会员.每月还有省钱着数等你拿"></div>
        <div v-if="!useUserStore().amm_vip && useUserStore().isVip" class="btn" @click="buyLinkMemberVip">
            <span>{{ state.link会员.开通Link着数会员 }}</span>
        </div>
        <div v-else-if="!useUserStore().amm_vip && !useUserStore().isVip" class="btn gray">
            <span>{{ state.link会员.仅支持后付用户开通Link着数会员 }}</span>
        </div>
        <div v-else class="btn" @click="emit('close')">
            <span>{{ state.link会员.知道了 }}</span>
        </div>
    </div>
    <img @click="emit('close')" :src="$imgs['closeIcon.png']" alt="" />
</template>

<script lang="ts" setup>
import { useUserStore } from '@/store'
import { useRouter, useLang, useEnvConfig } from 'hook'
const { state, lang } = useLang()
// const props = defineProps<{
//     title:string
//     desc:string
// }>()
const emit = defineEmits(['close'])

const { router } = useRouter()
const env = useEnvConfig()
const buyLinkMemberVip = () => {
    emit('close')
    if (env.RUN_ENV == 'develop' || env.RUN_ENV == 'uat') {
        location.href =
            'https://marketplaceuat.hk.chinamobile.com/marketHK/#/jetsoVip?token=<<cmcchkhsh_ssoToken>>&lang=<<cmcchkhsh_cmplang>>&shopCode=xxx'
    } else {
        location.href =
            'https://marketplace.hk.chinamobile.com/marketHK/#/jetsoVip?token=<<cmcchkhsh_ssoToken>>&lang=<<cmcchkhsh_cmplang>>&shopCode=xxx'
    }
}
</script>

<style lang="less" scoped>
.ad {
    width: 570px;
    background: #ffffff;
    border-radius: 72px 24px 72px 24px;
    background: #ffffff;
    padding: 48px 40px;
    .title {
        font-weight: 600;
        font-size: 32px;
        color: #4e5b7e;
        line-height: 28px;
        text-align: center;
    }
    .desc {
        text-align: center;
        padding: 24px 0;
        font-weight: 400;
        font-size: 24px;
        color: #6a6a6a;
    }
    .list {
        display: flex;
        align-items: stretch; /* 确保子元素拉伸填满容器高度 */
        justify-content: space-between;
        .list-item {
          display: flex;
          flex-direction: column;
          justify-content: space-between;
            padding: 16px;
            width: 230px;
            background: rgba(177, 129, 255, 0.18);
            border-radius: 16px 16px 16px 16px;
            .list-type {
                display: flex;
                align-items: center;
                justify-content: center;
                img {
                    width: 32px;
                    height: 32px;
                    margin: 0 auto;
                }
                span {
                    flex: 1;
                    padding-left: 8px;
                    display: block;
                    font-weight: 600;
                    font-size: 24px;
                    color: #5a32be;
                }
            }
            .list-desc {
                font-weight: 400;
                font-size: 20px;
                color: #6a6a6a;
                line-height: 30px;
                padding: 8px 0;
                span {
                    color: #ff550a;
                }
            }
            .list-box {
                display: flex;
                align-items: flex-end;
                position: relative;
                justify-content: space-between;
                .left {
                    width: 70px;
                    background: #3ac23d;
                    border-radius: 24px 8px 8px 8px;
                    padding: 6px 0;
                    img {
                        width: 42px;
                        height: auto;
                        margin: 0 auto;
                    }
                    span {
                        text-align: center;
                        display: block;
                        font-weight: 500;
                        font-size: 20px;
                        color: #ffffff;
                    }
                }
                > img.up {
                    height: 46px;
                    width: 36px;
                    margin: auto;
                    position: absolute;
                    bottom: 0;
                    left: 70px;
                }
                .right {
                    width: 96px;
                    border-radius: 24px 12px 8px 8px;
                    background: linear-gradient(180deg, #37c93a 0%, #0cb90d 100%);
                    border-radius: 16px 16px 16px 16px;
                    padding: 8px 6px 16px;
                    .bg {
                        width: 84px;
                        background: #ffffff;
                        border-radius: 20px 10px 8px 8px;
                        span {
                            padding-top: 6px;
                            display: block;
                            text-align: center;
                            font-weight: 500;
                            font-size: 20px;
                            color: #3ac23d;
                        }
                        img {
                            width: 72px;
                            margin: 0 auto;
                        }
                    }
                }
            }
        }
    }
    .tips {
        font-weight: 400;
        font-size: 24px;
        color: #6a6a6a;
        line-height: 40px;
        padding: 24px 0;
        text-align: center;
        span {
            color: #ff550a;
        }
    }
    .btn {
        width: 446px;
        padding: 0 10px;
        height: 84px;
        display: flex;
        align-items: center;
        justify-content: center;
        text-align: center;
        font-weight: 400;
        font-size: 36px;
        margin: 0 auto;
        color: #ffffff;
        text-align: center;
        background: linear-gradient(180deg, #9b7efe 0%, #7b5afc 100%);
        border-radius: 48px 48px 48px 48px;
        &.gray{
          font-size: 26px;
            background: gray;
        }
    }
}
img {
    width: 56px;
    height: 56px;
    margin: 58px auto 0;
    display: block;
}
</style>
