<template>
    <div class="container">
        <div class="home">
            <!-- 花园动效：云 -->
            <div id="gif">
                <!-- <img :src="$imgs['baiyun.png']" alt=""> -->
            </div>
            <!-- 全部领取 -->
            <AllGet
                @add="add"
                v-if="energyCount > 1 && friendStore.isOwn && (useTreeStore().hasPlanted || plantAll)"
                @updatePoint="updatePoint"
                :cardTimeOut="cardTimeOut"
                :nftEquity="nftEquity"
                :energyCount="energyCount"
            ></AllGet>
            <navComponent :returnType="2" :showTransmit="true" />
            <!-- 新手引导 -->
            <Guide ref="guideComponent" />
            <!-- <marquee class="marqueeBox" v-if="useUserStore().inLogin && friendStore.isOwn"/> -->
            <div class="background">
                <div class="bg-sky">
                    <div class="mountain" @click="toLink('sport')"></div>
                    <div class="house" @click="toLink('home')">
                        <div class="house-tips" v-html="state.advertisement.每週賺16200積分"></div>
                    </div>
                    <!-- 头像组件 -->
                    <headBox class="headbox" :totalEnergy="numberToThousands(useTotalEnergy_cpu)"></headBox>
                    <!-- 树 -->
                    <div class="treeType">
                        <template v-if="(buildTree && treeCode_cpu) || (!friendStore.isOwn && treeCode_cpu)">
                            <div class="treeType1" v-if="isFriend"></div>
                            <div class="type" @click="toRouter('/plantDetail/' + tree_id)">
                                <div class="type1">
                                    <div v-if="treeVip_cpu" class="typeLimit">
                                        <p>{{ state.home.後付限定 }}</p>
                                    </div>
                                    <!-- <span v-if="treeVip_cpu" class="vipzs">VIP</span> -->
                                    <span class="miao"></span>
                                    <p class="planting">
                                        {{ state.home.正在种植 }} {{ state.plant[treeCode_cpu]?.plantName }}
                                    </p>
                                    <p>></p>
                                </div>
                            </div>
                        </template>
                        <!--NFT权益列表-->
                        <div
                            class="usingCard"
                            :class="{ usingCardEn: lang == 'en' }"
                            ref="usingCard"
                            v-if="(cardTimeOut > 0 && friendStore.isOwn) || (nftEquity && useUserStore().isHK == 0)"
                        >
                            <van-swipe
                                :autoplay="5000"
                                style="height: 100%"
                                vertical
                                :show-indicators="false"
                                :touchable="false"
                            >
                                <van-swipe-item v-if="cardTimeOut > 0 && friendStore.isOwn">
                                    <p
                                        class="bottomtx"
                                        v-html="
                                            (cardType == 0 ? state.home.步数生效中 : state.home.减碳值生效中) +
                                            ': ' +
                                            cardTimeStr
                                        "
                                    ></p>
                                </van-swipe-item>
                                <van-swipe-item v-if="!isFriend && nftEquity && useUserStore().isHK == 0">
                                    <div class="bottomtx" @click="showActiveEquities">
                                        <!-- <template v-for="(item, index) in nftEquityList">
                                            <img class="icon" :key="index" :src="item.url" v-if="item.url" />
                                        </template> -->
                                        <img :src="$imgs[`nfticon.png`]" alt="" />
                                        <!-- <span>{{ state.nft.NFT權益生效中 }}</span> -->
                                        <span>{{ state.nft.NFT權益生效中 }}</span>
                                    </div>
                                </van-swipe-item>
                            </van-swipe>
                        </div>
                    </div>
                    <!-- 浇水 -->
                    <Watering
                        class="waterIcon"
                        :user_energy="user_energy"
                        :tree_energy="tree_energy"
                        :cardType="cardType"
                        ref="watering"
                        @init="init"
                        @handle="handle"
                        @addBuffer="addBuffer"
                    ></Watering>
                    <!-- 减碳球 -->
                    <div class="bubBalls" v-if="friendStore.isOwn && (useTreeStore().hasPlanted || plantAll)">
                        <div class="bub" v-if="virtualBub">
                            <div class="ball">
                                <img :src="$imgs[`icon/0.png`]" alt="" />
                                <div>+10g</div>
                            </div>
                        </div>
                        <div
                            v-for="(item, index) in bubArr"
                            :key="index"
                            @click="clickBub(index, item.energy_key, item.energy_value, item.expire_time)"
                            :class="{
                                bub: true,
                                bub1: index == 1,
                                bub2: index == 2,
                                bub3: index == 3,
                                bub4: index == 4,
                                move: moveArr[index]
                            }"
                        >
                            <p class="expire_time">
                                {{
                                    item && dayjs(item.expire_time).diff(dayjs(), 'hour') < 24
                                        ? dayjs(item.expire_time).diff(dayjs(), 'hour') >= 1
                                            ? state.home.x小时后消失.replace(
                                                  '{x}',
                                                  dayjs(item.expire_time).diff(dayjs(), 'hour')
                                              )
                                            : dayjs(item.expire_time).diff(dayjs(), 'minute') > 0
                                            ? state.home.x分钟后消失.replace(
                                                  '{x}',
                                                  dayjs(item.expire_time).diff(dayjs(), 'minute')
                                              )
                                            : 1
                                        : ''
                                }}
                            </p>
                            <div class="ball">
                                <img :src="$imgs[`icon/${item.source_icon}.png`]" alt="" v-if="item" />
                                <div>+{{ item?.energy_value }}g</div>
                            </div>
                            <p class="text">
                                {{
                                    item?.source_text.includes(' ')
                                        ? item?.source_text
                                        : item?.source_text.length > 8
                                        ? item?.source_text.slice(0, 8) + '\n' + item?.source_text.slice(8)
                                        : item?.source_text
                                }}
                            </p>
                        </div>
                    </div>
                    <div class="bubBalls1 bubBalls" v-if="!friendStore.isOwn">
                        <div
                            v-for="(item, index) in bubArr"
                            :key="index"
                            @click="
                                item.can_collect == 1
                                    ? clickBub(index, item.energy_key, item.energy_value, item.expire_time)
                                    : null
                            "
                            :class="{
                                bub: true,
                                bub1: index == 1,
                                bub2: index == 2,
                                bub3: index == 3,
                                bub4: index == 4,
                                move1: moveArr[index]
                            }"
                        >
                            <p v-show="moveArr[index]" class="addEnergy">+1g</p>
                            <div :class="{ ball1: true, tran: item.can_collect != 1 }">
                                <img :src="$imgs[`icon/${item.source_icon}.png`]" alt="" />
                                +{{ item.energy_value - frdSubArr[index] }}g
                            </div>
                            <p class="text">
                                <span v-if="item.can_collect == 1">
                                    {{ state.friend.可领取 }}
                                    <span class="xiushou"></span>
                                </span>
                            </p>
                        </div>
                    </div>
                    <!-- 树 -->
                    <div class="treeBox">
                        <TreeEntity
                            :class="{ treeEntity: true, shake: treeShake }"
                            :level="Number(treeLevel_cpu)"
                            :prop="[]"
                            :shake="true"
                            :treeCode="treeCode_cpu"
                            @treeTalk="treeTalk"
                            v-if="buildTree || (treeCode_cpu && !friendStore.isOwn)"
                        />
                        <img
                            class="nitu"
                            v-if="Number(treeLevel_cpu) > 0 && first_task_home && Number(treeLevel_cpu) <= 2"
                            :src="$imgs[`TT/state/泥土.png`]"
                            alt=""
                        />
                        <img
                            class="yinying"
                            v-if="Number(treeLevel_cpu) > 2 && treeCode_cpu != 'xiuqiuhua'"
                            :src="$imgs[`TT/state/${treeCode_cpu}/阴影.png`]"
                            alt=""
                        />
                        <img
                            class="yinying xiuqiu"
                            v-if="Number(treeLevel_cpu) > 2 && treeCode_cpu == 'xiuqiuhua'"
                            :src="$imgs[`TT/state/${treeCode_cpu}/阴影.png`]"
                            alt=""
                        />
                    </div>
                    <!-- 场景道具 -->
                    <sceneProps />
                    <div class="null" v-if="!buildTree">
                        <img :src="$imgs['null.png']" alt="" />
                    </div>
                    <!-- 树气泡 -->
                    <div :class="{ treeTalk: true, appear: showTreeTalk, vanish: !showTreeTalk }">
                        {{ treeTalkContent }}
                    </div>
                </div>
                <div class="bottomBox">
                    <img style="width: 100%" src="@assets/TT/leafs.png" alt="" />
                    <p :class="`addHp ${isRefresh ? 'addFlo' : ''}`">+{{ addHp }}HP</p>
                    <div
                        class="myEquip"
                        :class="{ equipPlated: !useUserStore().inLogin || !useTreeStore().hasPlanted }"
                        v-if="friendStore.isOwn"
                    >
                        <div class="equip taskEquip" @click="showTaskSheet" v-if="useUserStore().isHK == 0">
                            <img :src="$imgs[state.home.任务pic]" alt="" />
                            <img
                                :src="$imgs['task/state.png']"
                                alt=""
                                v-if="
                                    lang == 'en' &&
                                    first_task_home == taskStore.taskId.toString() &&
                                    !togetTask &&
                                    tasking
                                "
                                class="state"
                            />
                            <span
                                class="new"
                                v-else-if="tasking || togetTask || first_task_home != taskStore.taskId.toString()"
                            >
                                {{
                                    first_task_home != taskStore.taskId.toString()
                                        ? 'New'
                                        : togetTask
                                        ? state.task.待領取
                                        : state.task.進行中
                                }}
                            </span>
                        </div>
                        <!-- 左下角按钮列表 -->
                        <!--减碳商城-->
                        <div class="equip myMall" @click="toMall" v-if="friendStore.isOwn">
                            <div class="prompt" :class="{ promptEn: lang == 'en' }" v-if="!backpack">
                                <div class="promptText">
                                    <img :src="$imgs['package/horn.png']" alt="" />
                                    <div class="promptitle">{{ state.home.上線 }}</div>
                                </div>
                                <img class="triangle" :src="$imgs['package/triangle2.png']" alt="" />
                            </div>
                            <div class="new" v-if="commodity_newest_value">New</div>
                            <img :src="$imgs[state.home.mallpic]" />
                        </div>
                        <!--背包-->
                        <div class="equip" @click="toPackage">
                            <img :src="$imgs[state.home.宝箱pic]" alt="" />
                            <span v-if="package_point || cardAdd || cardAdd1" class="num"></span>
                        </div>
                        <!--活动-->
                        <div class="equip" @click="showActive">
                            <img :src="$imgs[state.home.活动pic]" alt="" />
                            <span v-if="no_day_login" class="num"></span>
                        </div>
                    </div>

                    <!-- 熊 -->
                    <div class="bear" :class="{ ceng: showBearTalk }">
                        <!-- 熊气泡 -->
                        <!-- <div :class="{ bearTalk: true, appear: showBearTalk, vanish: !showBearTalk }" v-if="!isFriend"> -->
                        <div :class="{ bearTalk: true, vanish: showBearTalk }" v-if="!isFriend && bearTalkContent">
                            <p>{{ bearTalkContent }}</p>
                            <!-- <div class="bbt" @click.stop="() => (showBearTalk = false)" v-if="bearTalkBtn == 0">
                                {{ state.bearTalk.知道了 }}
                            </div>
                            <div class="bbt" @click.stop="toRouter('/plantSelection')" v-if="bearTalkBtn == 1">
                                {{ state.bearTalk.去种植 }}
                            </div>
                            <div class="bbt" @click.stop="toPackageCard('tocard1')" v-if="bearTalkBtn == 3">
                                {{ state.package_.去查看 }}
                            </div> -->
                            <div class="bbt" @click.stop="bearTalkBtnFun" v-if="bearTalkBtnText">
                                {{ bearTalkBtnText }}
                            </div>
                        </div>
                        <img
                            class="bearJump"
                            @click="bearAni"
                            v-if="
                                (friendStore.isOwn && !propStore.propsConfig['GIF']) ||
                                (!friendStore.isOwn && !friendStore.propsConfig['GIF'])
                            "
                            :src="imgs[bearPic]"
                            alt=""
                        />
                        <!-- @click="toLink('grow')" -->
                        <vipGif180
                            v-if="friendStore.isOwn && propStore.propsConfig['GIF']"
                            :code="propStore.propsConfig['GIF']"
                        />
                        <vipGif180
                            v-if="!friendStore.isOwn && friendStore.propsConfig['GIF']"
                            :code="friendStore.propsConfig['GIF']"
                        />
                    </div>

                    <!-- mygame -->
                    <div
                        class="mygameBox"
                        v-if="useUserStore().inLogin && friendStore.isOwn && useTreeStore().hasPlanted"
                    >
                        <img class="points" :src="$imgs[`points_${lang}.png`]" alt="" />
                        <img :src="$imgs['mygame.png']" alt="" @click="openGame('')" class="mygameImg" />
                    </div>

                    <div class="treeCard" v-if="!useUserStore().inLogin">
                        <p class="tip">{{ state.home.快来种植属于你的植物吧 }}</p>
                        <div class="two-btn">
                            <div class="btn l" @click="toRouter('/plantSelection')">{{ state.home.逛逛花园 }}</div>
                            <div class="btn r" @click="goToLogin">{{ state.home.立即登入 }}</div>
                        </div>
                    </div>

                    <div class="treeCard" v-else-if="finLoad && !buildTree && !isFriend">
                        <van-loading text-size="16px" size="35" v-if="treeInfoLoading === false">
                            {{ state.loading.加载中 }}...
                        </van-loading>
                        <div v-else>
                            <p class="tip">{{ plantAll ? state.home.恭喜种完当前所有 : state.home.快去选择一颗 }}</p>
                            <div v-if="!plantAll" class="btn" @click="toRouter('/plantSelection')">
                                {{ state.home.开始种植 }}
                            </div>
                        </div>
                    </div>
                    <!-- 进度条入口 -->
                    <progressBar
                        class="progressBox"
                        v-else-if="finLoad && buildTree && friendStore.isOwn"
                        :level="level"
                        :progress="progress"
                        :rewardConfigs="reward_configs"
                        :treeCode="tree_code"
                        :propList="propList"
                        :tree_energy="tree_energy1"
                        :tree_total_energy="tree_total_energy"
                        :demand="demand"
                        @showProp="showProp"
                        @toRouter="toRouter"
                        @showAchieve="showAchieve"
                    ></progressBar>
                    <!-- <ArcProgressBar v-else-if="finLoad && buildTree && friendStore.isOwn" class="treeCard1"
                      :level = "level"
                      :progress = "progress"
                      :rewardConfigs = 'reward_configs'
                      :treeCode = 'tree_code'
                      :propList = 'propList'
                      :tree_energy = 'tree_energy1'
                      :tree_total_energy = 'tree_total_energy'
                      :demand = 'demand'
                      @showProp = 'showProp'
                      @toRouter = 'toRouter'
                      @showAchieve = 'showAchieve'
                    > </ArcProgressBar> -->
                    <div></div>
                </div>
            </div>
            <div class="rightIcons">
                <div class="topIcons">
                    <!--成就-->
                    <div class="ach" @click="clickShowSheet" v-if="friendStore.isOwn">
                        <img :src="$imgs[`成就/ach_${lang}.png`]" alt="" />
                        <span
                            class="num"
                            v-if="
                                (!firstList_sending_flowers ||
                                    equipAchieve.length != 0 ||
                                    firstList_two_nft != '1' ||
                                    nftAchieveRedPoint) &&
                                useUserStore().isHK == 0 &&
                                useUserStore().inLogin
                            "
                        ></span>
                        <div
                            class="bubble-text"
                            :class="{ 'bubble-text_en': lang == 'en' }"
                            v-if="!custom && useUserStore().isHK == 0"
                        >
                            <span>{{ state.flower.即日起獲得成就後便可贈送裝飾給好友 }}</span>
                        </div>
                    </div>
                    <div class="rule" v-if="friendStore.isOwn" @click="toRouter('/rule')">
                        <img :src="$imgs[`rule/rule_${lang}.png`]" alt="" />
                    </div>
                </div>
                <!-- <div class="navv city" v-if="!isSlash && friendStore.isOwn && versions < 10" @click="toLink('city')">
                    <p>{{state.home.城市}}</p>
                </div> -->
                <!-- 右上角图标列 -->
                <!--盲盒-->
                <div class="myBlindBottomIcon">
                    <!-- link会员菜单 -->
                    <Link-Member v-if="friendStore.isOwn"></Link-Member>
                    <van-badge :dot="blindRedPoint" :offset="[-8, 3]">
                        <div class="myBlind" v-if="friendStore.isOwn" @click="toBlind">
                            <p>{{ state.home.盲盒 }}</p>
                        </div>
                    </van-badge>
                    <!--nft背包--->
                    <div class="nftBag" v-if="friendStore.isOwn" @click="toNFTBag">
                        <img :src="$imgs[state.home.NFTBagpic]" />
                    </div>
                </div>
            </div>
            <div v-if="!friendStore.isOwn && friend_config.completedList.length > 0" class="friendAchieve">
                <!-- <div v-if="!friendStore.isOwn " class="friendAchieve"> -->
                <!-- <img class="friendComplete" :src="$imgs['friendComplete.png']" alt=""> -->
                <p class="tip">{{ state.friend.好友當前成就 }}</p>
                <div class="achContainer">
                    <achieve
                        class="ach"
                        type="mini"
                        :item="item"
                        v-for="(item, index) in friend_config.completedList"
                        :key="index"
                    />
                </div>
            </div>
            <div
                class="findnextfrd"
                @click="nextFriend"
                :class="{ findnextfrdTop: !useUserStore().inLogin || !useTreeStore().hasPlanted }"
            >
                <img :src="$imgs[`找减碳/${lang}.png`]" alt="" />
            </div>
            <ConfigProvider :theme-vars="themeVars">
                <ActionSheet :show="showTask" :lock-scroll="true">
                    <div class="sheetContent sheetContent3">
                        <div class="content">
                            <div class="xx2" @click="closeTask"></div>
                            <taskDialog :taskList="taskList" :batch="batchStatus"></taskDialog>
                        </div>
                    </div>
                </ActionSheet>
            </ConfigProvider>
            <!-- <ConfigProvider :theme-vars="themeVars">
                <ActionSheet :show="showSheet">
                    <div class="sheetContent">
                        <div class="content">
                            <div
                                class="xx"
                                @click="
                                    () => {
                                        showSheet = false
                                    }
                                "
                            ></div>
                            <div class="title">{{ state.home.我的成就 }}</div>
                            <div class="achcontain" data-isScroller="true">
                                <achieve
                                    @click="toInfo(item)"
                                    class="ach"
                                    type="small"
                                    :item="item"
                                    v-for="(item, index) in treeList"
                                    :key="index"
                                />
                            </div>
                        </div>
                    </div>
                </ActionSheet>
            </ConfigProvider> -->
            <ConfigProvider :theme-vars="themeVars">
                <ActionSheet :show="showFriendSheet" :lock-scroll="true">
                    <div class="sheetContent sheetContent2">
                        <div class="content">
                            <div
                                class="xx1"
                                @click="
                                    () => {
                                        showFriendSheet = false
                                    }
                                "
                            ></div>
                            <friendDialog :friendSheetTab="0" />
                        </div>
                    </div>
                </ActionSheet>
            </ConfigProvider>
            <ConfigProvider :theme-vars="themeVars">
                <ActionSheet :show="showActSheet" :lock-scroll="true">
                    <div class="sheetContent sheetContent2 sheetContent3">
                        <div class="content">
                            <div
                                class="xx1"
                                @click="
                                    () => {
                                        showActSheet = false
                                    }
                                "
                            ></div>
                            <friendDialog :friendSheetTab="1" />
                        </div>
                    </div>
                </ActionSheet>
            </ConfigProvider>
        </div>
        <div class="undersideBox" v-if="useUserStore().inLogin && useTreeStore().hasPlanted && useFriendStore().isOwn">
            <taskCarousel
                :taskList="taskList"
                :virtualBub="guide_completed"
                v-if="useUserStore().isHK == 0"
            ></taskCarousel>
            <friend :friendSheetTab="friendSheetTab" :virtualBub="guide_completed" class="friendComponent"></friend>
        </div>
        <div class="backToTop" id="back-to-top" @click="clickBackToTop">
            <img :src="$imgs['backToTop.png']" alt="" />
            <p>{{ state.backToTop }}</p>
        </div>
    </div>
</template>

<script lang="ts" setup>
import {
    getCurrentInstance,
    onBeforeUnmount,
    onMounted,
    ref,
    onBeforeMount,
    watch,
    reactive,
    nextTick,
    computed,
    provide
} from 'vue'
import {
    inApp,
    ExternalLink,
    logEventStatistics,
    getAppVersion,
    showHeaderRight,
    configureShare,
    getVersionForAosAndIos
} from '@via/mylink-sdk'
import { numberToThousands, stepCountStatus, nativeBackClick, getTreeList, getNowTime, getImageUrl } from '@unity/unity'
import {
    useTaskStore,
    useTreeStore,
    useUserStore,
    usePropStore,
    useAppStore,
    useFriendStore,
    useNFTStore,
    useAdawardStore
} from '@/store'
import { ActionSheet, ConfigProvider } from 'vant'
import propDialog from '@/components/propDialog.vue'
import activeEquities from '@/components/activeEquities.vue'
import upDialog from '@/components/upDialog.vue'
import awordDialog from '@/components/awordDialog.vue'
import drawDialog from '@/components/drawDialog.vue'
import TreeEntity from '@/components/treeEntity.vue'
import guideDialog from '@/components/guideDialog.vue'
import sceneProps from '@/components/sceneProps.vue'
import treasure from '@/components/treasure.vue'
import updateDialog from '@/components/updateDialog.vue'
import achieveDialog from '@/components/achieveDialog.vue'
import navComponent from '@/components/navComponent.vue'
import achieve from '@/components/achieve.vue'
import actDialog from '@/components/actDialog.vue'
import useCardDialog from '@/components/useCardDialog.vue'
import plantDialog from '@/components/plantDialog.vue'
import upgradeNFTDialog from '@/components/upgradeNFTDialog.vue'
import confirmUpgradeNFTDialog from '@/components/confirmUpgradeNFTDialog.vue'
import reCasteDialog from '@/components/reCasteDialog.vue'
import onlyOneDialog from '@/components/onlyOneDialog.vue'
import linkageDialog from '@/components/linkageDialog.vue'
import vipGif180 from '@/components/vipGif180.vue'
import friendDialog from '@/components/friendDialog.vue'
import taskDialog from '@/components/taskDialog.vue'
import rewardDialog from '@/components/rewardDialog.vue'
import marquee from '@/components/marquee.vue'
import plantTip from '@/components/plantTip.vue'
import refresh from '@/components/refresh.vue'
import taskOver from '@/components/taskOver.vue'
import firstLoginDialog from '@/components/firstLoginDialog.vue'
import progressBar from '@/components/progressBar.vue'
import taskCarousel from '@/components/taskCarousel.vue'

import friend from '@/components/friend.vue'
import buyNftDialog from '@/components/buyNftDialog.vue'
import LinkMember from './components/linkMember.vue'
import { imgs } from '@/assets/imgs'
import { TaskListType, TaskTimeType } from '@/store/modules/task/type'
//进度条
//   import ArcProgressBar from '@/components/ArcProgressBar.vue'
// 浇水
import Watering from './components/watering.vue'
// 全部领取
import AllGet from './components/allGet.vue'
import { getUserInfo } from '@via/mylink-sdk'
import {
    useDayjs,
    useLoading,
    useRouter,
    useDialog,
    useLang,
    useStorage,
    useEnvConfig,
    useToast,
    useEventBus,
    useWindow,
    usePageVisibility
} from 'hook'
import { useHomeConfig } from './home'
// 新手引导组件
import Guide from './components/Guide/guide.vue'
// 头像组件
import headBox from './components/headBox.vue'
import useNFT from '@/hooks/useNFT'
import { jumpMyGame } from '@/util/mygame-sdk'
const { judgePopUpNotification, showBearTalk } = useHomeConfig()
const { router, currentRoute } = useRouter()
const envConfig = useEnvConfig()
const propStore = usePropStore()
const nftStore = useNFTStore()
const taskStore = useTaskStore() //api
const friendStore = useFriendStore() //api
const { dayjs } = useDayjs()
const { state, lang } = useLang()
const loading = useLoading()
const { toast } = useToast()
const storage = useStorage()
const eventBus = useEventBus()
const { seeNFT } = useNFT()
const dialog = useDialog({
    linkageDialog,
    propDialog,
    upDialog,
    awordDialog,
    firstLoginDialog,
    guideDialog,
    treasure,
    updateDialog,
    achieveDialog,
    actDialog,
    plantDialog,
    drawDialog,
    useCardDialog,
    onlyOneDialog,
    friendDialog,
    plantTip,
    rewardDialog,
    refresh,
    taskOver,
    upgradeNFTDialog,
    reCasteDialog,
    buyNftDialog,
    activeEquities
})
// vant样式修改
let themeVars = {
    popupBackgroundColor: 'none',
    actionSheetMaxHeight: '100%'
}
// 是否是好友页面
let isFriend = ref(location.href.indexOf('friendId') !== -1)
// 新手引导的步数
let stepIndex = ref(0)
//减碳球arr
let point = ref([])

//5个减碳球位置
let bubArr = ref<any>([])
// 减碳球显示
let moveArr = ref<boolean[]>([false, false, false, false, false])
// 好友减碳值要减去的值
let frdSubArr = ref<number[]>([0, 0, 0, 0, 0])
// 植物升级对应减碳值
let demand = ref<number[]>([])
// 显示我的成就sheet
let showSheet = ref<boolean>(false)
// 显示我的任务
let showTask = ref<boolean>(false)
//树抖动
let treeShake = ref<boolean>(false)
// 是否正式pat
let isPat = envConfig.RUN_ENV == 'production' || envConfig.RUN_ENV == 'beta' ? true : false
// 熊的动画
let bearPicArr = [
    { img: 'bear/jump.gif', time: 1860 },
    { img: 'bear/shovel.gif', time: 4160 },
    { img: 'bear/think.gif', time: 4660 },
    { img: 'bear/wave.gif', time: 3620 },
    { img: 'bear/wipe-sweat.gif', time: 3980 }
]
let bearNum = ref(0) //熊的执行动作
let bearAniTimer: NodeJS.Timer | null = null
// 好友配置
let friend_config = ref({
    tree_code: '',
    level: '',
    user_energy: 0,
    req_vip: false,
    // headLogo: 'https://mylink.oss-cn-hongkong.aliyuncs.com/ico/sidebar/img_sidebar_profilephoto_unlogin.png',
    headLogo:
        'https://cdn.mylink.hk.chinamobile.com/via/resource/activity/2025-05-15/363c48415801449e8cb1fa0218f8aff0.png',
    completedList: []
})
/** 是否显示盲盒红点 */
const isShowBlindRedPoint = ref(false)
// 熊的图
let bearPic = ref('bear.png')

let buildTree = ref<boolean>(false) //是否有种树
let showTreeTalk = ref(false) //展示树气泡
let treeTalkContent = ref(state.treeTalk[0]) //树气泡内容
let bearTalkContent = ref('') //IP对话框内容
let bearTalkBtn = ref(2) //IP气泡按钮类型
let level = ref<number>(1) // 当前等级
let carbonVal = ref<number>(0) //当前减碳值
let progress = ref<number>(0) //当前等级的进度
let tree_code = ref<string>('') //正在种的树
let tree_id = ref<number>(0) //正在种的树的id
let user_energy = ref<number>(0) //用户持有的减碳值
let tree_energy = ref<number>(0) //对树减碳值
let tree_energy1 = ref<number>(0) //对树减碳值
let tree_total_energy = ref<number>(0) //对树总的减碳值
let treeList = ref() //树列表
let req_vip = ref(false) //当前树是否时vip专属
let firstDayLogin = ref(false) // 是否当日首次进入
let reward_configs = ref<Array<Record<string, any>>>([]) //此树的奖励道具列表
let guide_completed = ref(false) //用户是不是首次进入活动 false是
let stepNum = ref(0) //当前的步数
let hasCompleted = ref(false) //用户有没有种成过树
let wearList = ref<{ code: string; item_id: number }[]>([]) //已穿戴的道具
let no_first_login = ref(true) //二期活动第一次登录
let notified_first_logic1 = ref(true) //二期活动第一次登录
let notified_first_logic2 = ref(true) //三期活动第一次登录
let notified_first_logic3 = ref(true)
let notified_first_logic4 = ref(true)
let no_day_login = ref(false) //二期每日第一次登录
let package_point = ref(false) //背包红点
let cardAdd = ref(false) //背包红点
let cardAdd1 = ref(false) //背包红点
let achieve__dot = ref(false) //成就红点
let achieve__dots = ref(false) //成就组件内红点
let plantAll = ref(false) //是否种成了所有树
let acIntStu = ref(false) //积分活动是否在进行
let cardTimeOut = ref(0) //卡的过期倒计时
let cardTimeStr = ref('') //展示的时间
let cardType = ref(0) //卡片类型
let isRefresh = ref(false) //控制hp动画
let addHp = ref(0) //增加了多少生命值
let showHpani = ref(false) //激活hp动画
let lotArr: Array<any> = [] //抽奖奖励列表
let lotArr1: Array<any> = [] //抽奖奖励列表
let isSlash = ref(false) //是否是slash用户
let reissue_items = ref([]) //补发gif奖励
let energyCount = ref(0) //自己可领取总减碳球
let treeInfoLoading = ref(false) //树初始状态加载
let nftEquity = ref(false) //nft权益展示
/** nft权益展示图片 */
const nftEquityList = reactive<Array<{ code: string; url: string; groupId: string }>>([])
//送花版本首次进入
let custom = ref<Boolean>(true)
let backpack = ref<Boolean>(true)
let firstList_sending_flowers = ref(true)
let firstList_two_nft = ref('1')
let first_water_home = ref('')
let first_game_home = ref('')
let first_task_home = ref('') //任务栏第一次进入
let first_task3_home = ref('')
let first_task4_home = ref('')
let first_game_strategy = ref('')
// 最后一个月首次弹出任务即将结束
let first_pop_taskover = ref('')
let first_task_id = ref('')

let friendSheetTab = ref(1)
//ToDo
let showFriendSheet = ref(false)
let showActSheet = ref(false)

// 升级计数器
let upLevelCount = 0
// 升级弹窗计数器
let upDialogCount = 0
// 浇水组件
const watering = ref()

//页面加载缓冲v
let resol: (value?: string | PromiseLike<string> | undefined) => void = () => {}
let onWait = new Promise((resolve: (value?: string | PromiseLike<string> | undefined) => void) => {
    resol = resolve
})
// 获取步数缓冲
let resol2: (value?: string | PromiseLike<string> | undefined) => void = () => {}
let onWait2 = new Promise((resolve: (value?: string | PromiseLike<string> | undefined) => void) => {
    resol2 = resolve
})
let finLoad = ref(true) //是否显示卡片
let propList = ref<Record<string, Boolean>>({})
let isShowBackToTop = ref(false)
const { mQuery } = useWindow()
const preReward = ref(false) //是否已经判断有领取奖励
onBeforeMount(async () => {
    //   loading.loading('open')

    // 内地版新手指引减去送花，防止重复减
    if (useUserStore().isHK == 1 && state.guideStep.length == 18) {
        state.guideStep[5] = state.guideStep[16]
        state.guideStep[10] = state.guideStep[17]
        state.guideStep.splice(11, 2)
    }
    eventBus.on('closeFriendSheet', () => {
        showFriendSheet.value = false
    })
    eventBus.on('openFriendSheet', (num?) => {
        showActSheet.value = true
        if (num) {
            friendSheetTab.value = num
        }
    })
    eventBus.on('nextFriend', nextFriend)
    eventBus.once('isPlant', isPlant)
    eventBus.on('clickgrandfather', () => {
        showActSheet.value = false
        showSheet.value = true
    })
    eventBus.on('openActSheetDig', clickShowSheet)
    eventBus.on('taskToFriend', () => {
        showTask.value = false
        showFriendSheet.value = true
        useTaskStore().openTask = false
    })
    eventBus.on('taskToNFT', () => {
        showTask.value = false
        showSheet.value = true
    })
    eventBus.on('closeTask', () => {
        showTask.value = false
        useTaskStore().openTask = false
    })
    eventBus.on('nextTaskDia', () => {
        let rewardDialog2 = dialog.get('rewardDialog')
        rewardDialog2.on('close', () => {
            rewardDialog2.close()
            showTask.value = true
        })
        // 判断是否已完成,都未完成
        if (extraOne.value[2] == '0') {
            // 未完成
            rewardDialog2.show({
                aword: extraOne.value[1],
                extraInfo: true,
                isfinish: true,
                openTask: true,
                allTask: true
            })
            useUserStore().setUpData('first_task3_home', '1')
            useUserStore().setUpData('first_task4_home', '1')
        } else {
            //未领取或已结束
            rewardDialog2.show({
                aword: extraOne.value[1],
                extraInfo: true,
                isfinish: false,
                openTask: true,
                allTask: true
            })
            useUserStore().setUpData('first_task3_home', '1')
            useUserStore().setUpData('first_task4_home', '1')
        }
        first_task3_home.value = '1'
        first_task4_home.value = '1'
    })
    eventBus.on('getPreAward', () => {
        getTaskStatus()
        preReward.value = true
    })
    eventBus.on('updateEnergy', (num1) => {
        tree_energy.value += num1
        user_energy.value += num1
    })
    eventBus.off('upLevelFun', upLevelFun)
    eventBus.once('upLevelFun', upLevelFun)
    eventBus.on('rolandTaskState', taskInit)
    eventBus.on('refreshTask', refreshTask)
    eventBus.on('ifLotArr', ifLotArr)
    eventBus.on('upDataEnergyTotal', (num) => {
        user_energy.value = num
        tree_energy.value = num
    })
    const cns = getCurrentInstance()
    if (router.currentRoute.value.query.friendId) {
        friendStore.changeIsOwn(false)
        intoFriendPage(router.currentRoute.value.query.friendId)
    } else {
        friendStore.changeIsOwn(true)
    }
    cns && (await cns.appContext.config.globalProperties.$onWait)
    window.stepStatus = (obj) => {
        stepNum.value = Number(obj.stepNum)
        console.log(stepNum.value, '步数')
        resol2()
    }
    if (!useUserStore().inLogin) {
        //   loading.loading('close')
        resol() //释放await
        treeList.value = await taskStore.getTreeListNoLogin()
        useTreeStore().treeList = treeList.value
        treeList.value = treeList.value.trees
        return
    } else {
        console.log('进来了')
        await loadBeforeMount()
    }

    // 拉起任务栏
    if (useTaskStore().openTask) {
        showTaskSheet()
    }
})

onMounted(async () => {
    useAdawardStore().initAdaward()
    showSheet.value = false
    window.addEventListener('scroll', scrollFunction)
    document.querySelector('#dialog-guideDialog')?.remove()
    document.querySelector('#dialog-firstLoginDialog')?.remove()
    let dialogShow = document.querySelector('#app-dialog') as HTMLElement
    dialogShow.style.display = 'block'
    await onWait
    // 我的花園頁面訪問量
    logEventStatistics('Mygarden_home_page')

    document.querySelector('.addHp')?.addEventListener('animationend', function (e) {
        //绑定事件.
        isRefresh.value = false
    })
    // if (friendStore.isOwn) {
    loadMount()
    // }
    //   loading.loading('close')
    // 判断版本
    const version = getAppVersion()
    const [first, seconds] = version.split('.')
    // if (firstDayLogin.value && (Number(first) < 9 || (Number(first) == 9 && Number(seconds) < 5))) {

    // 内地不判断版本
    if (useUserStore().isHK == 0) {
        if (firstDayLogin.value && Number(first) < 11) {
            // if (firstDayLogin.value) {
            dialog.get('updateDialog').show({}, { maskClose: false })
        }
    }
    setTimeout(() => {
        custom.value = true
        // let dom = document.querySelector('.bubble-text')
        // if (dom) {
        //     dom.remove()
        // }
    }, 5000)
    if (window.localStorage.getItem('pushSuccess')) {
        toast(state.flower.發送成功)
        showSheet.value = true
        window.localStorage.setItem('pushSuccess', '')
    }
    bearAniTimer = setInterval(() => {
        bearAni()
    }, 10000)
    showSheet.value = nftStore.nftsConfig.isActivityOpen
    //   marqueeCard()//权益跑马灯

    // 根据盲盒状态显示红点
    const blindRes = await taskStore.getBlindBoxState()
    if (blindRes) {
        isShowBlindRedPoint.value = blindRes.red_dot_state
    }
    // toast('等多幾秒可能會有短片, 睇完可多5g減碳值哦~')
})
watch(usePageVisibility(), async () => {
    if (!useUserStore().inLogin) {
        return
    }
    virtualBub.value = false
    if (!usePageVisibility().isHidden) {
        // location.reload()
        if (inApp.value) {
            const version = await getVersionForAosAndIos()
            if (version < 1060) {
                if (router.currentRoute.value.name == 'home') {
                    const urlObj = new URL(location.href.split('#')[0])
                    const shareUrl = urlObj.toString()
                    configureShare({
                        url: shareUrl,
                        title: state.share.title,
                        content: state.share.desc,
                        img: imgs['share.png']
                    })
                }
            } else {
                if (!router.currentRoute.value.query.friendId) {
                    showHeaderRight(location.origin + location.pathname + '#/home')
                }
            }
        }
        await loadBeforeMount()
    } else {
        bearAniTimer && clearInterval(bearAniTimer)
    }
})

watch(
    () => useTreeStore().planted,
    async () => {
        if (useTreeStore().planted) {
            await init()
        }
    }
)

let treeCode_cpu = computed(() => {
    return friendStore.isOwn ? tree_code.value : friend_config.value.tree_code
})

// let useHeadLogo_cpu = computed(() => {
//     return friendStore.isOwn ? useUserStore().headLogo : friend_config.value.headLogo
// })

let treeLevel_cpu = computed(() => {
    return friendStore.isOwn ? level.value : friend_config.value.level
})

let treeVip_cpu = computed(() => {
    return friendStore.isOwn ? req_vip.value : friend_config.value.req_vip
})

let useTotalEnergy_cpu = computed(() => {
    return friendStore.isOwn ? user_energy.value : friend_config.value.user_energy
})

/** 是否显示盲盒红点的计算 */
const blindRedPoint = computed(() => {
    return isShowBlindRedPoint.value && !isFriend.value
})

const loadBeforeMount = async () => {
    if (inApp.value) {
        stepCountStatus({ callbackName: 'stepStatus' })
    } else {
        resol2()
    }
    await onWait2
    await init()

    //获取减碳球
    let energy: Record<string, any> = {}
    if (friendStore.isOwn) {
        energy = await taskStore.getEnergy(stepNum.value) //让后端接口增加nft数据
        if (energy.energies.length >= 1) {
            //>1修改为 >= 1
            energyCount.value = energy.energies.length
        } else {
            energyCount.value = -1
        }
        console.log(energy, '减碳球', energyCount.value)
    } else {
        energy = await friendStore.friendActives(router.currentRoute.value.query.friendId as string)
        frdSubArr.value = [0, 0, 0, 0, 0]
    }
    point.value = energy.energies.filter((item) => {
        return dayjs(item.expire_time).diff(dayjs()) > 0
    })
    console.log('减碳球的位置', point.value)
    bubArr.value = point.value.slice(0, point.value.length > 5 ? 5 : point.value.length)
    resol()
    //小熊对话
    console.log('当日首次进入：' + firstDayLogin.value)
    // 全部领取模块
    // if(point.value.length > 0){
    //     showOrHide()
    // }

    if (useUserStore().isHK == 0) {
        await taskInit()
    }

    if (friendStore.isOwn) {
        // TODO无补发道具 无会员奖励
        // if (reissue_items.value.length > 0) {
        //     bearTalk('补发道具')
        //     console.log('补发道具')
        // } else if (cardAdd1.value && useUserStore().isVip) {
        //     bearTalk('会员奖励')
        // } else if (!guide_completed.value) {
        //     bearTalk('首次进入活动')
        // } else if (firstDayLogin.value) {
        //     bearTalk('当日首次')
        // } else if (point.value.length > 0) {
        //     bearTalk('当有减碳球待领取时')
        // } else if (point.value.length == 0) {
        //     bearTalk('无减碳球时')
        // }
        bearTalk()
    }
}

const loadMount = async () => {
    if (!useUserStore().inLogin) {
        return
    }
    if (!backpack.value) {
        setTimeout(() => {
            useUserStore().setUpData('first_enter_backpack', '1')
            backpack.value = true
        }, 5000)
    }
    // 是否开启新手引导
    if (!guide_completed.value && buildTree.value) {
        showBearTalk.value = false
        // no_first_login.value = true
        let pro = new Promise((resolve) => {
            if (bubArr.value.length == 0) {
                virtualBub.value = true
            }
            resolve(1)
        })
        pro.then(() => {
            guideComponent.value && guideComponent.value.guideFn()
        })
        return
    }
    //一期活动引导
    if (!no_first_login.value && buildTree.value) {
        showBearTalk.value = false
        let pro = new Promise((resolve) => {
            if (bubArr.value.length == 0) {
                virtualBub.value = true
            }
            resolve(1)
        })
        pro.then(() => {
            stepIndex.value = 2
            guideComponent.value && guideComponent.value.guideFn()
        })
        return
    }
    judgePopUpNotification(tree_energy.value, demand.value[level.value], () => {
        openNFTDialog()
    })
    // 点击种植后产生的action
    if (buildTree.value) {
        let action = useTaskStore().getAction()
        action.forEach((item) => {
            if (item.action_key == 'tree_upgrade' || item.action_key == 'tree_complete') {
                let reward_items = item.tree_value.reward_items
                let integral_value = item.integral_value || 0
                // 升级后 赋值风控状态
                let integralStatus = item.integral_status
                addBuffer(
                    item.tree_value.level - 1,
                    item.tree_value.level,
                    reward_items,
                    integral_value,
                    integralStatus
                )
            }
        })
    }
}

const ifLotArr = async () => {
    // 获取功能卡
    let cardRes = await taskStore.getPull()
    let actionArr = cardRes.user_actions
        .filter((item) => item.action_key == 'item_receive')
        .map((item) => item.item_value)
    lotArr = actionArr
    if (lotArr.some((item) => item.code == 'x2energy')) {
        logEventStatistics('garden_get_double_crexp')
    }
    if (lotArr.some((item) => item.code == 'x2wewalk')) {
        logEventStatistics('garden_get_double_wewalk_crexp')
    }
    if (lotArr.length > 0) {
        cardAdd.value = true
        cardAdd1.value = true
        eventBus.on('useCarding', (info) => {
            useUserStore().updatePackageFuncFlag()
            useUserStore().updatePackageFuncFlag1()
            cardAdd.value = false
            cardAdd1.value = false
            useCardFn(info.user_actions[0].item_value)
        })
        let drawDia = dialog.get('drawDialog')
        drawDia.beforeClose(
            () =>
                new Promise(async (resolve) => {
                    linkDigFn()
                    resolve(false)
                })
        )
        drawDia.on('toPackage', () => {
            drawDia.close()
            useUserStore().updatePackageFuncFlag()
            useUserStore().updatePackageFuncFlag1()
            toRouter('/package')
            usePropStore().toCard = true
        })
        drawDia.on('useCard', (item) => {
            drawDia.close()
            dialog.get('useCardDialog').show({ type: 1, kind: item.type, item }, { maskClose: false })
        })
        drawDia.show({ awordArr: lotArr })
    }
}

let virtualBub = ref(false) //气泡球的显示
// 新手引导组件实例
const guideComponent = ref<InstanceType<typeof Guide> | null>(null)

// 任务栏初始化
const taskList = ref<TaskListType[]>([])
// 任务栏更新状态
const batchStatus = ref<TaskTimeType>()
// 待领取标识
const togetTask = ref<Boolean>(false)
// 任务中标识
const tasking = ref<Boolean>(false)
// 额外任务是啥
const extraOne = ref<string[]>([])
const extraTwo = ref<string[]>([])
const taskInit = () => {
    return new Promise(async (resolve, reject) => {
        await taskStore
            .getTaskList()
            .then((data) => {
                const { tasks, batch, banners } = data
                taskStore.banner = banners
                console.log(taskStore.banner)

                // batch.stop_at = "2024-03-26 10:53:20"
                batchStatus.value = batch
                taskStore.taskId = batch ? batch.id : taskStore.taskId
                let getNum = 0
                let dotask = 0
                tasks.filter((item) => {
                    item.status == 1 ? (getNum += 1) : (getNum += 0)
                    item.status == 0 ? (dotask += 1) : (dotask += 0)
                    // 判断是否额外任务触发
                    if (
                        !extraOne.value.length &&
                        item.code == 'use-ticket-equity' &&
                        (item.expired_at || (!item.expired_at && item.status == 1))
                    ) {
                        //null 任务code 任务状态 弹窗字段
                        extraOne.value = [item.expired_at, item.code, item.status, first_task3_home.value]
                    }
                    if (!extraTwo.value.length && item.code == 'finish-all' && item.status == 1) {
                        extraTwo.value = [item.expired_at, item.code, item.status, first_task4_home.value]
                    }
                })
                getNum >= 1 ? (togetTask.value = true) : (togetTask.value = false)
                dotask >= 1 ? (tasking.value = true) : (tasking.value = false)
                taskList.value = tasks
                // taskList.value[7].status = 0
                console.log('任务列表', taskList.value)

                resolve(true)
            })
            .catch((err) => {
                reject(err)
            })
    })
}

// TODO 最后一个月结束弹窗
// 最后一个月内只会触发一次，在代码中触发一次后续将不再触发。
let locks = false
const lastMonthPopTaskOver = () => {
    if (useUserStore().isHK != 0) return
    if (!useTreeStore().hasPlanted) return
    // 防止在init中多次调用
    if (locks) return

    // 获取当前时间
    const _getDateNow = async (): Promise<number> => {
        const dateNow_GMT = ((await getNowTime()) as string) || '' // GMT 时间字符串
        const networkTime = Math.floor(new Date(dateNow_GMT).getTime() / 1000)
        const localTime = Math.floor(new Date().getTime() / 1000)
        return networkTime || localTime
    }

    taskStore.getTaskList().then(async (res) => {
        // 任务结束return
        if (!res?.batch?.stop_at) {
            return
        }
        // 倒计时存在
        if (!(res.batch && res.batch.stop_at)) return

        const DAY_30 = 2592000 // 30天转换的秒数
        const taskOver = dialog.get('taskOver') // 获取任务结束弹窗
        // 绑定任务栏开启
        eventBus.on('showTaskSheet', () => {
            taskOver.beforeClose(async () => {
                showTaskSheet()
                return false
            })
        })

        // 计算时间差
        let dateNow = await _getDateNow()
        let time = Math.floor(new Date(res.batch.stop_at.replace(/-/g, '/')).getTime() / 1000)
        const countdown = +time - +dateNow

        // 任务批次切换允许弹窗
        const task_id = res.batch.id
        task_id !== first_task_id.value && (first_pop_taskover.value = '0')

        // 是否在30天内且允许弹出
        if (countdown <= DAY_30 && String(first_pop_taskover.value) === '0') {
            taskOver.show({}, { maskClose: false })
            // 开锁
            locks = true
            // 已经弹出则不允许弹出
            first_pop_taskover.value = '1'
            first_task_id.value = task_id
            useUserStore().setUpData('first_pop_taskover', '1')
            // 弹出后更新存储的任务批次id
            useUserStore().setUpData('first_task_id', task_id)
        } else if (countdown > DAY_30) {
            // 还未到达30天就可以弹出
            useUserStore().setUpData('first_pop_taskover', '0')
        }
    })
}
// 刷新任务列表
const refreshTask = async () => {
    taskStore.limitTask = true
    const refreshInfo = await taskInit()
    if (refreshInfo === true) {
        toast(state.task.刷新成功)
        getTaskStatus()
    } else {
        toast(state.task.發生異常)
        dialog.closeAll()
        let firstLoginDia = dialog.get('refresh')
        firstLoginDia.show(
            { refresh: true, btn: state.task.刷新任務, tips: state.task.刷新任务文本 },
            { maskClose: false }
        )
    }
}
// 打开任务栏
const showTaskSheet = async () => {
    taskStore.limitTask = false
    if (!useUserStore().inLogin) {
        await useUserStore().login()
        if (useUserStore().inLogin) {
            await loadBeforeMount()
            await loadMount()
        } else return
    }
    logEventStatistics('garden_task_icon')
    await taskInit()
    if (!first_task_home.value || first_task_home.value != taskStore.taskId.toString()) {
        useUserStore().setUpData('first_task_home', taskStore.taskId)
        first_task_home.value = taskStore.taskId.toString()
    }
    triggerDia()
}

const triggerDia = () => {
    // 判断是否触发额外奖赏
    if (!extraOne.value.length && !extraTwo.value.length) {
        showTask.value = true
    } else {
        let rewardDialog = dialog.get('rewardDialog')
        rewardDialog.on('close', () => {
            rewardDialog.close()
            showTask.value = true
        })
        // 两种都有
        if (extraOne.value.length && extraTwo.value.length && !first_task3_home.value && !first_task4_home.value) {
            rewardDialog.show({
                aword: extraTwo.value[1],
                extraInfo: true,
                nextTask: true,
                isfinish: extraTwo.value[2] === '0'
            })
        } else if (extraOne.value.length && !first_task3_home.value) {
            // 只触发第一种
            // 未完成
            if (extraOne.value[2] == '0') {
                rewardDialog.show({
                    aword: extraOne.value[1],
                    isfinish: true,
                    extraInfo: true,
                    openTask: true,
                    allTask: true
                })
            } else {
                rewardDialog.show({
                    aword: extraOne.value[1],
                    isfinish: false,
                    extraInfo: true,
                    openTask: true,
                    allTask: true
                })
            }
            useUserStore().setUpData('first_task3_home', '1')
            first_task3_home.value = '1'
        } else if (extraTwo.value.length && !first_task4_home.value) {
            // 只触发第二种
            rewardDialog.show({
                aword: extraTwo.value[1],
                isfinish: false,
                extraInfo: true,
                openTask: true
            })
            useUserStore().setUpData('first_task4_home', '1')
            first_task4_home.value = '1'
        } else {
            showTask.value = true
        }
    }
}

// 关闭任务栏
const closeTask = async () => {
    await taskInit()
    showTask.value = false
    useTaskStore().openTask = false
}

// 判断任务状态
const getTaskStatus = async () => {
    if (useUserStore().isHK == 1) return
    const taskState = await taskStore.getTaskState()
    if (!taskState.previous_reward) {
        lastMonthPopTaskOver()
        return
    } else {
        // 弹出上期奖励弹窗
        let rewardDia = dialog.get('rewardDialog')
        rewardDia.on('close', () => {
            rewardDia.close()
        })
        // TODO 奖励完成后触发最后一个月任务提醒
        const reward_energy = taskState.previous_reward.energy_value // 奖励的减碳值
        const arrive_energy = reward_energy + tree_energy1.value // 到达的减碳值
        rewardDia.beforeClose(() => {
            return new Promise((resolve) => {
                // 如果会升级延后到奖励领取完再执行最后一个月提醒
                if (arrive_energy >= demand.value[level.value]) {
                    locks = true
                }
                lastMonthPopTaskOver()
                resolve(false)
            })
        })
        // 两种奖励都有
        if (taskState.previous_reward.energy_value && taskState.previous_reward.item_codes) {
            const reward = taskState.previous_reward.item_codes.split(',')
            rewardDia.show({ aword: taskState.previous_reward.energy_value, aword2: reward.length, extraInfo: false })
            // 只有减碳卡
        } else if (!taskState.previous_reward.energy_value) {
            const reward = taskState.previous_reward.item_codes.split(',')
            rewardDia.show({ aword2: reward.length, extraInfo: false })
            // 只有减碳值
        } else {
            rewardDia.show({ aword: taskState.previous_reward.energy_value, extraInfo: false })
        }
    }
}
const linkDigFn = async () => {
    if (useUserStore().isVip && useTreeStore().hasPlanted) {
        let vipPull = await propStore.vipPull()
        lotArr1 = vipPull.user_actions
            .filter((item) => item.action_key == 'item_receive')
            .map((item) => item.item_value)
        // lotArr1 = test
    } else {
        // 上一期奖励领取
        if (
            ((!preReward.value && useUserStore().isVip) || !useUserStore().isVip) &&
            useUserStore().isHK == 0 &&
            buildTree.value
        ) {
            getTaskStatus()
        } else {
            lastMonthPopTaskOver()
        }
        return
    }
    if (lotArr1.length > 0) {
        let linkageDia = dialog.get('linkageDialog')
        linkageDia.beforeClose(
            () =>
                new Promise(async (resolve) => {
                    // 上一期奖励领取
                    if (
                        ((!preReward.value && useUserStore().isVip) || !useUserStore().isVip) &&
                        useUserStore().isHK == 0 &&
                        buildTree.value
                    ) {
                        getTaskStatus()
                    } else {
                        lastMonthPopTaskOver()
                    }
                    resolve(false)
                })
        )
        linkageDia.on('toPackage', () => {
            useUserStore().updatePackageFuncFlag()
            useUserStore().updatePackageFuncFlag1()
            cardAdd1.value = false
            cardAdd.value = false
            linkageDia.close()
            toRouter('/package')
            usePropStore().toCard1 = true
        })
        init()
        linkageDia.show({ awordArr: lotArr1 })
    } else {
        // 上一期奖励领取
        if (
            ((!preReward.value && useUserStore().isVip) || !useUserStore().isVip) &&
            useUserStore().isHK == 0 &&
            buildTree.value
        ) {
            getTaskStatus()
        } else {
            lastMonthPopTaskOver()
        }
    }
}
onBeforeUnmount(() => {
    loading.loading('close')
    eventBus.off('nextFriend', nextFriend)
    eventBus.off('isPlant', isPlant)
    eventBus.off('ifLotArr', ifLotArr)
    window.removeEventListener('scroll', scrollFunction)
    cardTimer && clearInterval(cardTimer)
    bearAniTimer && clearInterval(bearAniTimer)
})

async function nextFriend() {
    logEventStatistics('garden_find_frds_click')
    if (!useUserStore().inLogin) {
        return await useUserStore()
            .login()
            .then(async () => {
                await loadBeforeMount()
            })
    }
    showFriendSheet.value = true
    showActSheet.value = false
}

async function intoFriendPage(friendId) {
    if (!useUserStore().inLogin) {
        return await useUserStore().login()
    }
    console.log('进入好友页面')
    friend_config.value = {
        tree_code: '',
        level: '',
        user_energy: 0,
        req_vip: false,
        // headLogo: 'https://mylink.oss-cn-hongkong.aliyuncs.com/ico/sidebar/img_sidebar_profilephoto_unlogin.png',
        headLogo:
            'https://cdn.mylink.hk.chinamobile.com/via/resource/activity/2025-05-15/363c48415801449e8cb1fa0218f8aff0.png',
        completedList: []
    }
    let res = await Promise.all([friendStore.toFriendPage(friendId)])
    let info = res[0]
    if (info.friend_tree) {
        friend_config.value.tree_code = info.friend_tree.code
        friend_config.value.level = info.friend_tree.level
        friend_config.value.req_vip = info.friend_tree.req_vip
    }
    friend_config.value.completedList = info.friend_completed_trees
    friend_config.value.completedList.forEach((item: { completed: Boolean }) => {
        item.completed = true
    })
    friend_config.value.user_energy = info.friend.energy_total
    friendStore.propsHandle(info.friend_active_items)
    propStore.propsHandle(info.friend_active_items)
    if (document.querySelector('.nav .title') as HTMLElement) {
        ;(document.querySelector('.nav .title') as HTMLElement).innerText = info.friend.name
    }
}

const initMoveArr = () => {
    moveArr.value.forEach((_, index) => {
        moveArr.value[index] = false
    })
}

watch(
    router.currentRoute,
    async (newV, oldV) => {
        if (!useUserStore().inLogin) {
            return
        }
        try {
            isFriend.value = location.href.indexOf('friendId') !== -1
            loading.loading('open')
            if (newV.query.friendId) {
                showActSheet.value = false
                friendStore.changeIsOwn(false)
                initMoveArr()
                await intoFriendPage(newV.query.friendId)
                let res: Record<string, any> = await friendStore.friendActives(newV.query.friendId as string)
                point.value = res.energies.filter((item) => {
                    return dayjs(item.expire_time).diff(dayjs()) > 0
                })
                point.value.sort(function (a: { can_collect: number }, b: { can_collect: number }) {
                    return b.can_collect - a.can_collect
                })
                bubArr.value = point.value.slice(0, point.value.length > 5 ? 5 : point.value.length)
                loading.loading('close')
            } else if (newV.name == 'home' && !newV.query.friendId) {
                friendStore.changeIsOwn(true)
                initMoveArr()
                friendStore.friendActivity()
                let energy = await taskStore.getEnergy(stepNum.value)
                point.value = energy.energies.filter((item) => {
                    return dayjs(item.expire_time).diff(dayjs()) > 0
                })
                point.value.sort(function (a: { can_collect: number }, b: { can_collect: number }) {
                    return b.can_collect - a.can_collect
                })
                bubArr.value = point.value.slice(0, point.value.length > 5 ? 5 : point.value.length)
                await init()
                await loadMount()
                loading.loading('close')
            }
            frdSubArr.value = [0, 0, 0, 0, 0]
        } catch (error) {
            console.log(error)
            window.location.reload()
            throw error
        }
    },
    { deep: true }
)

watch(tree_energy, (newV, oldV) => {
    let diff = newV - oldV
    if (diff > 0 && showHpani.value) {
        addHp.value = diff
        isRefresh.value = true
    }
})

watch(cardTimeOut, () => {
    cardTimeStr.value = getDuration(cardTimeOut.value)
})

const equipAchieve = ref([] as Array<any>)
const count = ref(0)
const commodity_newest_value = ref(false)
const active_equities = ref([])
const showActiveEquities = () => {
    let activeEquitiesDig = dialog.get('activeEquities')
    activeEquitiesDig.show({
        list: active_equities.value
    })
}
// TODO 刷新信息
const init = async (sw: boolean = false) => {
    let info = await taskStore.getInfo() //状态数据
    if (info.code === 401) {
        //判断是否登录显示加载中
        treeInfoLoading.value = true
    }
    useUserStore().name = info.user.name
    useUserStore().third_id = info.user.third_id
    isShowMyCity()
    console.log(useTaskStore().upLevelPlantList)
    if (
        useTaskStore().upLevelPlantList.length &&
        useTaskStore().upLevelPlantList[useTaskStore().upLevelPlantList.length - 1].action_key == 'tree_complete'
    ) {
        upLevelPlantFun(useTaskStore().upLevelPlantList)
        useTaskStore().upLevelPlantList = []
        return
    } else {
        useTaskStore().upLevelPlantList = []
    }
    let a = await Promise.all([
        taskStore.getTreeList(), //成就的数据
        taskStore.getPropList(), //道具信息
        taskStore.getPropList(0, 1),
        friendStore.getFriendList(), //好友数据
        friendStore.friendActivity()
    ])
    commodity_newest_value.value =
        info.commodity_newest_value !== 0 &&
        info.commodity_newest_value >
            Number(
                info.webui_manage_values?.commodity_newest_value ? info.webui_manage_values?.commodity_newest_value : 0
            )
    // frdSubArr.value = [0, 0, 0, 0, 0]
    treeList.value = a[0]
    // 判断是否有旧的权益
    //TODO NFT
    equipAchieve.value = treeList.value.trees.filter((value) => {
        return (
            value.completed &&
            value.nft_exists &&
            (value.nft_actived_notice || value.nft_upgrade_notice) &&
            value.code !== 'huangzhongmu' &&
            value.code !== 'shuishirong'
        )
    })
    for (const item of equipAchieve.value) {
        if (item.nft_actived_notice) {
            await nftStore.userNftActivedNoticeMute(item.tree_id)
        }
    }
    let equipAchieveTemp = treeList.value.trees.filter((value) => {
        return value.nft_upgrade_notice
    })

    if (equipAchieveTemp.length != 0) {
        firstList_two_nft.value = info.webui_manage_values ? info.webui_manage_values.first_list_two_nft : ''
        useUserStore().setUpData('first_list_two_nft', '1')
    }

    useTreeStore().treeList = a[0]
    useTreeStore().nowTree = info

    // 初始化权益图标
    const nftEquityRes = await taskStore.getNFTEquityState()
    active_equities.value = nftEquityRes.active_equities

    // 记录nft权益的数据
    nftStore.nftEquity = nftEquityRes
    nftEquityList.splice(0, nftEquityList.length)
    if (Array.isArray(nftEquityRes.equity_codes) && nftEquityRes.equity_codes.length > 0) {
        nftEquityRes.equity_trees.forEach((value, index) => {
            let url = ''
            const tree = useTreeStore().treeList?.trees.find((tree) => {
                return tree.code == value.code
            })
            url = imgs[`NTFDetail/equity_small/v2/${value.code}.png`]
            nftEquityList.push({
                url,
                code: value.code,
                groupId: value.nft_group_id
            })
        })
    }
    // 初始化nft权益列表
    nftEquity.value = nftEquityRes.activied

    useUserStore().userInfo = info
    if (treeList.value.trees) {
        treeList.value = treeList.value.trees
    }
    useUserStore().day_limit = info.watering_info.day_limit
    useUserStore().waterNum = info.watering_info.day_count
    // 到达上限数-1时，需要立即关闭气泡。防止出现时间间隔，导致同时出现三秒浇水以及明天再来的气泡
    if (useUserStore().waterNum >= useUserStore().day_limit - 1) {
        if (watering.value) watering.value.Tseconds = false
    }
    // 判断是否关闭浇水按钮
    if (useUserStore().waterNum >= useUserStore().day_limit) {
        if (watering.value) watering.value.closeOpenWater()
    }
    first_water_home.value = info.webui_manage_values ? info.webui_manage_values.first_water_home : ''
    first_task_home.value = info.webui_manage_values
        ? info.webui_manage_values.first_task_home
        : taskStore.taskId.toString()
    first_task3_home.value = info.webui_manage_values ? info.webui_manage_values.first_task3_home : ''
    first_task4_home.value = info.webui_manage_values ? info.webui_manage_values.first_task4_home : ''
    first_game_home.value = info.webui_manage_values ? info.webui_manage_values.first_game_home : ''
    first_game_strategy.value = info.webui_manage_values ? info.webui_manage_values.first_game_strategy : ''
    first_pop_taskover.value = info?.webui_manage_values?.first_pop_taskover || '0'
    first_task_id.value = info?.webui_manage_values?.first_task_id || taskStore.taskId.toString()
    custom.value = info.webui_manage_values && info.webui_manage_values.first_sending_flowers
    backpack.value = info.webui_manage_values && info.webui_manage_values.first_enter_backpack
    firstList_sending_flowers.value = info.webui_manage_values
        ? info.webui_manage_values.first_list_sending_flowers == '0'
            ? false
            : true
        : false

    let propArr = [...a[1].items, ...a[2].items]
    propArr.forEach((item) => {
        propList.value[item.code] = item.owned
    })
    console.log(propList.value, '获取了啥道具')
    treeInfoLoading.value = true
    useUserStore().changeVip(info.user.vip)
    useUserStore().changeAMM_VIP(info.user.amm_vip)
    if (Array.isArray(treeList.value) || treeList.value) {
        hasCompleted.value = treeList.value.some((item) => {
            return item.completed == true
        })
        plantAll.value = !treeList.value.some((item) => {
            console.log(item.completed == false, item.req_vip ? item.req_vip === useUserStore().isVip : true)
            return item.completed == false && (item.req_vip ? item.req_vip === useUserStore().isVip : true)
        })
    }
    if (info.tree) {
        //有种树
        useTreeStore().changeHasPlanted(true)
        storage.save(`Forest ${envConfig.RUN_ENV}-${useUserStore().phone}-HasPlanted`, true)
        buildTree.value = true
        let arr = info.tree.level_configs.map((item) => item.energy_value)
        demand.value = [0, ...arr]
        console.log('减碳指标:' + demand.value)
        tree_code.value = info.tree.code
        tree_id.value = info.tree.tree_id
        level.value = info.tree.level
        // 交互9/21更新：去掉登录14天直升3级
        if (friendStore.isOwn) {
            wearList.value = info.active_items
        }
        tree_energy.value = info.tree.energy_total

        console.log('总减碳值:', info.tree.energy_total)
        tree_energy1.value = info.tree.energy_total

        tree_total_energy.value = info.tree.target_energy_total
        req_vip.value = info.tree.req_vip
        reward_configs.value = info.tree.level_configs
        reward_configs.value.push({
            // 'energy_value': 90,
            level: 10,
            reward_items: '成就'
        })
        useTreeStore().changeTreeCode(tree_code.value)
        useTreeStore().changelv(level.value)
        handle()
    } else {
        if (location.href.includes('treeNFT')) return
        //没种树
        if (hasCompleted.value && !plantAll.value && !sw && count.value == 0) {
            // 控制只弹一次
            count.value++
            let guideDlg = dialog.get('guideDialog')
            guideDlg.on('goToPlant', () => {
                guideDlg.close()
                toRouter('/plantSelection')
            })
            guideDlg.show({}, { maskClose: false })
        }
        useTreeStore().changeHasPlanted(false)
        storage.save(`Forest ${envConfig.RUN_ENV}-${useUserStore().phone}-HasPlanted`, false)
        buildTree.value = false
    }

    if (info.active_used_items.length > 0) {
        useCardFn(info.active_used_items[0])
    }

    if (friendStore.isOwn) {
        usePropStore().propsHandle(info.active_items)
    }

    reissue_items.value = info.reissue_items //补发的道具
    // reissue_items.value = [
    //     {"item_id": 1071,"code": "arborist"}
    // ]//补发的道具
    // console.log(reissue_items.value, "补发的东西")

    user_energy.value = info.user.energy_total //用户总的减碳值
    useTreeStore().changeEnergyTotal(info.user.energy_total)
    guide_completed.value = info.guide_completed //是否已完成新手引导
    //   guide_completed.value = false//是否已完成新手引导
    no_first_login.value = info.notified_first_login //二期活动弹窗
    notified_first_logic1.value = info.notified_first_logic1 //三期活动弹窗
    notified_first_logic2.value = info.notified_first_logic2
    notified_first_logic3.value = info.notified_first_logic3
    notified_first_logic4.value = info.notified_first_logic4
    no_day_login.value = !info.notified_activity //活动小红点
    package_point.value = info.flag_on_received_item //背包小红点
    cardAdd.value = info.flag_on_received_func_item //背包小红点（功能卡）
    cardAdd1.value = info.flag_on_received_vip_item //背包小红点（会员领取卡）
    isSlash.value = info.user.slash
    useUserStore().changeSlash(isSlash.value)
    acIntStu.value = info.actived_integral_activity
    taskStore.changeAcIntStu(acIntStu.value)
    firstDayLogin.value = info.first_day_login

    // 这里注释是因为这里直接弹窗会直接遮挡其他弹窗，目前已经在其他流程下存在这个弹窗
    // 先判断一遍是否有上期奖励弹窗，有的话lastMonthPopTaskOver放到上期奖励弹窗后再执行
    // if (useUserStore().isHK == 0) {
    //     let taskState = await taskStore.getTaskState()
    //     !taskState.previous_reward && guide_completed.value && lastMonthPopTaskOver()
    // }

    first_water_home.value = removeTimeTips(first_water_home.value, 'first_water_home')
    first_game_home.value = removeTimeTips(first_game_home.value, 'first_game_home')
    first_game_strategy.value = removeTimeTips(first_game_strategy.value, 'first_game_strategy')

    // 根据盲盒状态显示红点
    const blindRes = await taskStore.getBlindBoxState()
    if (blindRes) {
        isShowBlindRedPoint.value = blindRes.red_dot_state
    }
}

// 固定时间后标志消失
const removeTimeTips = (word, keyName) => {
    if (!word) {
        word = dayjs().format('YYYY-MM-DD')
        useUserStore().setUpData(keyName, dayjs().format('YYYY-MM-DD').replace(/-/g, ''))
    } else if (word != '1') {
        let str = word.toString()
        let time = 7 * 24 * 60 * 60 * 1000
        str = str.slice(0, 4) + '-' + str.slice(4, 6) + '-' + str.slice(6, 8)
        str = str.replace(/-/g, '/')
        if (new Date(str).getTime() + time < new Date().getTime()) {
            useUserStore().setUpData(keyName, '1')
            word = '1'
        }
    }
    return word
}

//用户登录
const goToLogin = async () => {
    loading.loading('open')
    await useUserStore().login()
    if (!useUserStore().inLogin) return loading.loading('close')
    const version = await getVersionForAosAndIos()
    if (version < 1060) {
        if (router.currentRoute.value.name == 'home') {
            const urlObj = new URL(location.href.split('#')[0])
            const shareUrl = urlObj.toString()
            configureShare({
                url: shareUrl,
                title: state.share.title,
                content: state.share.desc,
                img: imgs['share.png']
            })
        }
    } else {
        if (!router.currentRoute.value.query.friendId) {
            showHeaderRight(location.origin + location.pathname + '#/home')
        }
    }
    console.log('进入goToLogin-----执行分享按钮函数')

    await loadBeforeMount()
    await loadMount()
    loading.loading('close')
}
//使用功能卡
let cardTimer: NodeJS.Timer | null = null
const useCardFn = (info) => {
    cardTimer && clearInterval(cardTimer) //使用前清除一遍累加器
    useUserStore().changeUsingCard(true)
    cardTimeOut.value = dayjs(info.expired_at).diff(dayjs())
    let code = info.code
    if (code == 'x2energy') {
        cardType.value = 1
    } else if (code == 'x2wewalk') {
        cardType.value = 0
    }
    if (cardTimeOut.value > 0) {
        //防抖（时间计时倒数）
        cardTimer = setInterval(() => {
            if (cardTimeOut.value <= 0) {
                cardTimeOut.value = 0
                cardTimer && clearInterval(cardTimer)
            } else {
                cardTimeOut.value -= 1000
            }
        }, 1000)
    }
}

// 功能卡及权益跑马灯
const marqueeCard = async () => {
    // if(nftStore.nftsConfig.isOwn === false || cardTimeOut.value <= 0){
    //     return
    // }
    // const marquee: any = document.querySelector('.marquee')
    // let usingCard:Element = document.querySelector('.usingCard') as Element
    // console.log(usingCard.children,'childrenchildrenchildrenchildrenchildrenchildrenchildrenchildrenchildrenchildren');
    // let bottomtx:DOMRect = document.querySelector(`.bottomtx`)?.getBoundingClientRect() as DOMRect
    // let durations = 5000//时间间隔
    // let curIndex = 0;//目前第几项
    // let eleHeight = bottomtx.height
    // setInterval(()=> {
    //     let from = curIndex * eleHeight //开始滚动高度
    //     curIndex++
    //     let to = curIndex * eleHeight // 下一次的滚动高度
    //     let totalDuration = 500 // 变化的总时间
    //     let duration = 10 // 变化的时间间隔
    //     let totalNum = totalDuration / duration // 变化的次数
    //     let dis = (to - from) / totalNum //每一帧变化的高度
    //     let timer = setInterval(()=>{
    //         from += dis
    //         // 到达一次滚动的高度停止计时器
    //         if(from >= to){
    //             clearInterval(timer)
    //             // 滚动完成时回到初始位置
    //             if(curIndex === usingCard.children.length) {
    //                 let firstDom = document.querySelectorAll(`.bottomtx`)[curIndex-1]
    //                 // usingCard.removeChild(usingCard?.firstChild as ChildNode)
    //                 // usingCard.appendChild(firstDom)
    //                 usingCard.appendChild(firstDom as ChildNode)
    //             }
    //         }
    //         usingCard.scrollTop = from
    //     },duration)
    // },durations)
}

// 监听事件方法
const hiddenPrevent = (evt) => {
    let isScroller = evt.path.some((item) => {
        return item.dataset?.isscroller == 'true'
    })
    if (!isScroller) {
        evt.preventDefault()
    }
}

//  更新减碳球数量
async function updatePoint(callback) {
    //获取减碳球
    let energy: Record<string, any> = {}
    if (friendStore.isOwn) {
        energy = await taskStore.getEnergy(stepNum.value) //让后端接口增加nft数据
        if (energy.energies.length >= 1) {
            //>1修改为 >= 1
            energyCount.value = energy.energies.length
        } else {
            energyCount.value = -1
        }
        console.log(energy, '减碳球', energyCount.value)
    } else {
        energy = await friendStore.friendActives(router.currentRoute.value.query.friendId as string)
        frdSubArr.value = [0, 0, 0, 0, 0]
    }
    point.value = energy.energies.filter((item) => {
        return dayjs(item.expire_time).diff(dayjs()) > 0
    })
    energyCount.value = 0
    moveArr.value.forEach((item, index) => {
        moveArr.value[index] = true
    })
    console.log(point.value) //减碳球总数
    let energy_key_arr = point.value.map((item: Record<string, any>) => {
        return item.energy_key
    })
    add(energy_key_arr)
}

// 读取缓存字段
const loadHandle = () => {
    let no_day_login_point = storage.load(`Forest ${envConfig.RUN_ENV}-${useUserStore().phone}-no_day_login_point`)
    let package_point_load = storage.customLoad(
        `via:system:Forest ${envConfig.RUN_ENV}-${useUserStore().phone}-package_point`
    )
    if (typeof no_day_login_point != undefined) {
        no_day_login.value = no_day_login_point //二期活动弹窗
    } else {
        let endDay = dayjs(dayjs().add(1, 'day').format('YYYY-MM-DD 00:00:00')).diff(dayjs(), 's')
        storage.save(`Forest ${envConfig.RUN_ENV}-${useUserStore().phone}-no_day_login_point`, false, endDay)
    }
    if (package_point_load) {
        package_point.value = package_point_load.v
    } else {
        storage.save(`Forest ${envConfig.RUN_ENV}-${useUserStore().phone}-package_point`, false)
    }
}

// 格式化时间
const getDuration = (ms) => {
    let time = Number(ms)
    let hours: number | string = Math.floor((time % 86400000) / 3600000)
    let minutes: number | string = Math.floor((time % 3600000) / 60000)
    let seconds: number | string = Math.floor((time % 60000) / 1000)
    hours = hours < 10 ? '0' + hours : hours
    minutes = minutes < 10 ? '0' + minutes : minutes
    seconds = seconds < 10 ? '0' + seconds : seconds
    return hours + ':' + minutes + ':' + seconds
}

// 点击减碳球
let timer: NodeJS.Timer | null = null
let energy_key_arr: string[] = [] //整合数组
const clickBub = async (index: number, energy_key: string, energy_value: number, expire_time: string) => {
    console.log('clickBub添加的能量', energy_value)
    if (buildTree.value) {
        showHpani.value = true //启动hp动画
    }
    if (!guide_completed.value || !no_first_login.value) {
        return
    }
    if (buffTimer) {
        //已经在运行升级弹窗了，别运行
        return
    }
    // if (buildTree.value == false) {
    //     //没种树收集减碳球时重定向ection')
    //     // TODO可删除
    //     // bearTalk('未种植时点击减碳球')
    //     return
    // }
    if (dayjs(expire_time).diff(dayjs()) < 0) {
        toast(state.dialog.减碳球已过期)
    } else {
        energy_key_arr.push(energy_key)
    }
    // if (!friendStore.isOwn && !friend_config.value.tree_code) {
    //     toast(state.friend.好友未种植)
    //     return
    // }
    // 节流
    timer && clearTimeout(timer)
    timer = setTimeout(async () => {
        let reqArr = JSON.parse(JSON.stringify(energy_key_arr))
        energy_key_arr = []
        if (friendStore.isOwn) {
            await add(reqArr)
        } else {
            await add_frd(reqArr, index)
        }
    }, 500)
    if (friendStore.isOwn) {
        const handler = (e) => {
            if (!moveArr.value[index]) return
            point.value.splice(index, 1)
            if (point.value[4]) {
                bubArr.value[index] = point.value[4]
                moveArr.value[index] = false
            } else {
                bubArr.value[index] = null
            }
            document.getElementsByClassName('bub')[index].removeEventListener('animationend', handler)
        }
        //动画控制
        document.getElementsByClassName('bub')[index].addEventListener('animationend', handler)
        // energyCount.value -= 1
        moveArr.value[index] = true
    }
}

function isPlant() {
    if (!useTreeStore().hasPlanted) {
        dialog.get('plantTip').show({})
        return false
    } else {
        return true
    }
}

// 获取好友减碳球
const add_frd = async (arr: string[], index?: number) => {
    console.log(arr, '朋友减碳球', index)
    let res = await friendStore.friendClick(arr, router.currentRoute.value.query.friendId as string)
    if (!res) {
        await loadBeforeMount()
        return
    }
    //优化Number
    if (Number(index)) {
        moveArr.value[index as number] = true
    }
    if (res.success_energy_keys.length) {
        taskStore.setUserActions(res.user_actions)
        bubArr.value.forEach((item: any, index) => {
            res.success_energy_keys.forEach((i) => {
                if (i == item.energy_key) {
                    item.can_collect = 0
                    frdSubArr.value[index] += 1
                }
            })
        })
    } else {
        bubArr.value.forEach((item: any) => {
            item.can_collect = 0
        })
    }
    await init()
}

//  获取点击减碳球获取减碳值
const add = async (arr: string[]) => {
    let res = await taskStore.clickEnergy(arr)
    let uplevel = 0
    if (!res) return
    let energyadd = res.user_actions.filter((item) => item.action_key == 'energy_add')

    let upgrade = res.user_actions
        .filter((item) => item.action_key == 'tree_upgrade')
        .sort((a, b) => {
            return a.tree_value.level - b.tree_value.level
        })
    let complete = res.user_actions.filter((item) => item.action_key == 'tree_complete')
    let action = [...upgrade, ...complete]
    if (action.length == 0) {
        energyadd.forEach((item) => {
            if (item.action_key == 'energy_add') {
                tree_energy.value += item.energy_value
                tree_energy1.value += item.energy_value
                user_energy.value += item.energy_value
                handle()
            }
        })
    } else {
        action.forEach((item) => {
            if (item.action_key == 'tree_upgrade' || item.action_key == 'tree_complete') {
                uplevel = item.tree_value.level
                let reward_items = item.tree_value.reward_items
                let integral_value = item.integral_value || 0
                // 减碳球升级 积分风控
                let integralStatus = item.integral_status
                addBuffer(
                    item.tree_value.level - 1,
                    item.tree_value.level,
                    reward_items,
                    integral_value,
                    integralStatus
                )
            }
        })
    }
    energyCount.value -= res.success_energy_keys.length
    // await init()
    await useTaskStore()
        .getInfo()
        .then((res) => {
            if (res.user.energy_total) {
                console.log(res.user.energy_total, '总数')
            }
            useTreeStore().changeEnergyTotal(res.user.energy_total)
        })
}

const upLevelPlantFun = async (user_actions) => {
    console.log(user_actions)
    if (!tree_code.value) {
        tree_code.value = user_actions[0].tree_value.code
    }
    let uplevel = 0
    let energyadd = user_actions.filter((item) => item.action_key == 'energy_add')

    let upgrade = user_actions
        .filter((item) => item.action_key == 'tree_upgrade')
        .sort((a, b) => {
            return a.tree_value.level - b.tree_value.level
        })
    let complete = user_actions.filter((item) => item.action_key == 'tree_complete')
    let action = [...upgrade, ...complete]
    useTaskStore().upLevelPlantList = []
    if (action.length == 0) {
        energyadd.forEach((item) => {
            if (item.action_key == 'energy_add') {
                tree_energy.value += item.energy_value
                tree_energy1.value += item.energy_value
                user_energy.value += item.energy_value
                handle()
            }
        })
    } else {
        action.forEach((item) => {
            if (item.action_key == 'tree_upgrade' || item.action_key == 'tree_complete') {
                uplevel = item.tree_value.level
                let reward_items = item.tree_value.reward_items
                let integral_value = item.integral_value || 0
                // 减碳球升级 积分风控
                let integralStatus = item.integral_status
                addBuffer(
                    item.tree_value.level - 1,
                    item.tree_value.level,
                    reward_items,
                    integral_value,
                    integralStatus
                )
            }
        })
    }
    // await init()
    await useTaskStore()
        .getInfo()
        .then((res) => {
            if (res.user.energy_total) {
                console.log(res.user.energy_total, '总数')
            }
            useTreeStore().changeEnergyTotal(res.user.energy_total)
        })
}

//缓冲器————一级一级升
let bufferQue: Array<Array<number | Record<string, string | number>[]>> = []
let buffTimer: NodeJS.Timeout | null = null
const addBuffer = function (
    treeLevelForm: number,
    treeLevelTo: number,
    reward_items: Record<string, string | number>[],
    integral_value: number,
    integralStatus: number
) {
    //箭头函数没有 arguments
    bufferQue.push([treeLevelForm, treeLevelTo, reward_items, integral_value, integralStatus])
    if (!buffTimer) {
        buffTimer = setTimeout(() => {
            if (bufferQue.length > 0) {
                upFn(treeLevelForm, treeLevelTo, reward_items, integral_value, integralStatus)
            }
            buffTimer = null
        }, 500)
    }
}

const popBuffer = function () {
    let shift = bufferQue.shift()
    if (bufferQue.length == 0) {
        buffTimer = null
        if (shift && shift[1] != 10) {
            //目标等级
            init(true)
        }
        return
    } else {
        const [treeLevelForm, treeLevelTo, reward_items, integral_value, integralStatus] = bufferQue[0]
        upFn(
            treeLevelForm as number,
            treeLevelTo as number,
            reward_items as Record<string, string | number>[],
            integral_value as number,
            integralStatus as number
        )
    }
}

/**
 * 升级函数
 * @param from 升级前等级
 * @param to 升级后的等级
 * @param reward_items 奖励列表
 * @param integral_value 积分值
 * @param integralStatus 风控状态 0 失败、1 成功、-1 风控
 */
const upFn = (
    from: number,
    to: number,
    reward_items: Record<string, string | number>[],
    integral_value?: number,
    integralStatus?: number
) => {
    // function upFn(from:number, to:number, reward_items:Array<any>, integral_value?: number, integralStatus?: number){
    // package_point.value = true//背包小红点
    // storage.save(`Forest ${envConfig.RUN_ENV}-${useUserStore().phone}-package_point`, true)
    let updia = dialog.get('upDialog') //升级/成就
    let trsdia = dialog.get('treasure') //宝箱
    let aworddia = dialog.get('awordDialog') //奖励
    console.warn(from, to, reward_items, integral_value, integralStatus)
    // 宝箱弹窗绑定函数
    trsdia.on('trsClose', () => {
        trsdia.close()
        // 开启奖励弹窗
        aworddia.show(
            {
                tree_id: tree_id,
                awordArr: reward_items,
                integral_value: integral_value,
                integral_Status: integralStatus,
                treeCode: tree_code,
                to: to
            },
            { maskClose: false }
        )
    })
    aworddia.on('wearProp', (item) => {
        aworddia.close()
        wearProp(item)
    })
    aworddia.on('toPackage', () => {
        aworddia.close()
        toRouter('/package')
    })
    updia.beforeClose(
        () =>
            new Promise(async (resolve) => {
                if (reward_items.length) {
                    if (to == 10) {
                        await init()
                    } else {
                        trsdia.show({}, { maskClose: false })
                    }
                } else {
                    if (to == 10) {
                        await init()
                    }
                }
                resolve(false)
            })
    )
    aworddia.beforeClose(
        () =>
            new Promise(async (resolve) => {
                if (to == 10) {
                    // updia.show({from: from, to: to, treeCode: tree_code }, {})
                    if (tree_code.value != 'huangzhongmu' && tree_code.value != 'shuishirong') {
                        useUserStore().setUpData('first_list_sending_flowers', '0')
                    }
                    await init()
                }
                // 每次奖励弹窗关闭，进行计数，待当前弹窗数与升级次数相同时，才弹最后一个月弹窗
                // 保证在所有弹窗关闭后再弹最后一个月弹窗
                upDialogCount += 1
                if (upDialogCount === upLevelCount) {
                    locks = false
                    lastMonthPopTaskOver()
                    upDialogCount = 0
                    upLevelCount = 0
                }
                popBuffer()
                resolve(false)
            })
    )
    //正式弹窗
    if (to == 10) {
        trsdia.show({}, { maskClose: false })
    } else {
        // init(true)
        updia.show({ from: from, to: to, treeCode: tree_code }, {})
    }
}

// upFn(1, 2, [{ code: 'item18' }, { item_id: 1131 }], 150, 1)
// 计算等级，进度条
const handle = () => {
    if (level.value != 10) {
        progress.value =
            (tree_energy1.value - demand.value[level.value - 1]) /
            (demand.value[level.value] - demand.value[level.value - 1])
        if (progress.value > 1) {
            progress.value = 1
        }
    } else if (level.value == 10) {
        progress.value = 0
    }
}

// 显示道具弹窗
const showProp = (code: string) => {
    dialog.get('propDialog').show({
        isInfo: false,
        propInstance: {
            icon: imgs[`props/${state.propsReward[code].propType}/${code}.png`],
            propName: state.propsReward[code].propName,
            propValue: state.propsReward[code].propValue
        }
    })
}

// 显示成就弹窗
const showAchieve = (item) => {
    dialog.get('achieveDialog').show(
        {
            item: {
                code: tree_code,
                completed: true,
                req_vip: req_vip.value
            },
            reword: Object.assign({ code: item }, state.propsReward[item])
        },
        { maskClose: false }
    )
}

// 路由跳转
const toRouter = (url: string, obj: object = {}) => {
    if (url == '/package') {
        // storage.save(`Forest ${envConfig.RUN_ENV}-${useUserStore().phone}-package_point`, false)
        // useUserStore().updatePackageFlag()
    }
    router.push({
        path: url,
        query: {
            hideNavigationBar: 'true',
            ...obj
        }
    })
}

const toPackageCard = (name) => {
    toRouter('/package')
    switch (name) {
        case 'tocard1':
            usePropStore().toCard1 = true
            break
        case 'tocard2':
            usePropStore().toCard2 = true
            break
    }
}

const toPackage = async () => {
    toRouter('/package', { cardAdd: cardAdd1.value || cardAdd.value })
}

const toMall = () => {
    logEventStatistics('garden_cr_mall_icon_click')
    if (useUserStore().inLogin) {
        useUserStore().setUpData('first_enter_backpack', '1')
    }
    router.push({
        path: '/mall',
        query: {
            hideNavigationBar: 'true'
        }
    })
}

// TODO 应该写到环境变量配置中
let blindUrl = {
    uat: `openurl-modal://http://*************/activity/myCity/#/blindBox?lang=<<cmcchkhsh_cmplang>>&forceHideNavigationBar=true`,
    beta: `openurl-modal://https://mylink.komect.com/activity/myCity/#/blindBox?lang=<<cmcchkhsh_cmplang>>&forceHideNavigationBar=true&debug=true`,
    pat: `openurl-modal://https://mylink.komect.com/activity/myCity/#/blindBox?lang=<<cmcchkhsh_cmplang>>&forceHideNavigationBar=true`
}
const toBlind = async () => {
    logEventStatistics('garden_blindbox_icon_click')
    window.location.href =
        useEnvConfig().RUN_ENV == 'production'
            ? blindUrl.pat
            : useEnvConfig().RUN_ENV == 'beta'
            ? blindUrl.beta
            : blindUrl.uat
}

/** 不同环境下的背包地址 */
let nftBagUrl = {
    uat: `openurl-modal://http://*************/nftweb/#/third-party?origin=4&target=myNft&tabIndex=0&categoryId=4&lang=<<cmcchkhsh_cmplang>>&forceHideNavigationBar=true&categoryId=4`,
    beta: `openurl-modal://https://cdn.mylink.hk.chinamobile.com/blockchain/link/nft/index.html#/third-party?origin=14&target=myNft&tabIndex=0&lang=%3C%3Ccmcchkhsh_cmplang%3E%3E&forceHideNavigationBar=true`,
    pat: `openurl-modal://https://cdn.mylink.hk.chinamobile.com/blockchain/link/nft/index.html#/third-party?origin=14&target=myNft&tabIndex=0&lang=%3C%3Ccmcchkhsh_cmplang%3E%3E&forceHideNavigationBar=true`
}

/** 跳转nft背包地址 */
const toNFTBag = () => {
    // 添加锚点
    logEventStatistics('garden_nft_bag')
    window.location.href =
        useEnvConfig().RUN_ENV == 'production'
            ? nftBagUrl.pat
            : useEnvConfig().RUN_ENV == 'beta'
            ? nftBagUrl.beta
            : nftBagUrl.uat
}

// 使用道具
const wearProp = (item: { item_id: number; code: string }) => {
    let code = item.code.replace(/(\d)+/g, '')
    if (code == 'item') {
        if (level.value < 3) {
            toast(state.dialog.植物太小啦)
            return
        } else if (level.value < 6) {
            let itemm = propStore.propsConfig['挂饰'][0]
            if (itemm) {
                propStore.wearProp(false, itemm.item_id)
                let p: { item_id: number; code: string } | undefined = wearList.value.find(
                    (item) => item.code.replace(/(\d)+/g, '') == 'item'
                )
                if (p) {
                    let index = wearList.value.indexOf(p)
                    wearList.value.splice(index, 1)
                }
            }
            wearList.value.push(item)
            propStore.wearProp(true, item.item_id)
        } else {
            let propsHanging = wearList.value.filter((item) => item.code.replace(/(\d)+/g, '') == 'item')
            if (propsHanging.length < 3) {
                propStore.wearProp(true, item.item_id)
                wearList.value.push(item)
            } else {
                toast(state.dialog.已经挂满啦)
            }
        }
    } else {
        let p: { item_id: number; code: string } | undefined = wearList.value.find(
            (item) => item.code.replace(/(\d)+/g, '') == code
        )
        if (typeof p == 'undefined') {
            wearList.value.push(item)
        } else {
            let index = wearList.value.indexOf(p)
            wearList.value.splice(index, 1)
            propStore.wearProp(false, p.item_id)
            wearList.value.push(item)
        }
        propStore.wearProp(true, item.item_id)
    }
    usePropStore().propsHandle(wearList.value)
}

//控制树的气泡
let treeTime: NodeJS.Timeout | null = null
let shakeTime: NodeJS.Timeout | null = null
const treeTalk = () => {
    if (location.href.indexOf('friendId') !== -1) {
        return
    }
    if (showTreeTalk.value) {
        showTreeTalk.value = false
    } else {
        if (!treeTime) {
            let index = ~~(Math.random() * state.treeTalk.length)
            treeTalkContent.value = state.treeTalk[index]
            showTreeTalk.value = true
            treeTime = setTimeout(() => {
                showTreeTalk.value = false
                treeTime && clearTimeout(treeTime)
                treeTime = null
            }, 5000)
        }
    }
    //树抖
    if (!treeShake.value) {
        logEventStatistics('garden_hint_click')
        treeShake.value = true
        shakeTime = setTimeout(() => {
            treeShake.value = false
        }, 1000)
    }
}

//控制IP的气泡
let bearTime: NodeJS.Timeout | null = null
let bearTalkBtnText = ref('')
let bearTalkBtnFun = ref()
const isBearTalk = ref(false)
const bearTalk = () => {
    if (isBearTalk.value) return
    isBearTalk.value = true
    let bearText = [
        {
            text: state.bearTalk.收集到的減碳值將在新植物種植時,
            btnText: '',
            showRule: function () {
                return plantAll.value
            }
        },
        {
            text: state.bearTalk.首次进入活动,
            btnText: '',
            showRule: function () {
                return !guide_completed.value
            }
        },
        {
            text: state.bearTalk.当日首次,
            btnText: '',
            showRule: function () {
                return firstDayLogin.value
            }
        },
        {
            text: state.bearTalk.当有减碳球待领取时,
            btnText: '',
            showRule: function () {
                return point.value.length > 0
            }
        },
        {
            text: state.bearTalk.淋花三次後,
            btnText: '',
            showRule: function () {
                return !(useUserStore().waterNum >= useUserStore().day_limit)
            }
        },
        {
            text: state.bearTalk.限定任務即將到期,
            btnText: state.bearTalk.去查看,
            showRule: function () {
                return taskList.value.some((value) => value.status === 0)
            },
            btnFun: () => {
                showTaskSheet()
            }
        },
        {
            text: state.bearTalk.无减碳球时,
            btnText: state.bearTalk.去查看,
            showRule: function () {
                return point.value.length == 0
            },
            btnFun: () => {
                toRouter('upStrategy')
            }
        },
        //TODO NFT
        {
            text: state.bearTalk.鑄造NFT,
            btnText: state.bearTalk.去鑄造,
            showRule: function () {
                return treeList.value?.some(
                    (tree) =>
                        tree.completed &&
                        !tree.nft_exists &&
                        !tree.nft_upgrade_notice &&
                        tree.code !== 'huangzhongmu' &&
                        tree.code !== 'shuishirong'
                )
            },
            btnFun: () => {
                toRouter('achievement')
            }
        },
        {
            text: state.bearTalk.遊戲上新,
            btnText: state.bearTalk.去遊玩,
            showRule: function () {
                let nowTime = new Date().getTime()
                return nowTime < 1736524799000
            },
            btnFun: () => {
                openGame('46')
            }
        },
        {
            text: state.bearTalk.NFT可回收,
            btnText: state.bearTalk.去回收,
            showRule: function () {
                return treeList.value?.some(
                    (tree) =>
                        (tree.nft_exists && !tree.nft_recycled) ||
                        (tree.nft_upgrade_notice && !tree.nft_recycled_before_upgrade)
                )
            },
            btnFun: () => {
                toRouter('achievement')
            }
        }
    ]
    const startTimer = () => {
        bearTime && clearInterval(bearTime)
        let tempBearText = bearText.filter((value) => {
            return value.showRule()
        })
        let textNum = ref(0)
        bearTime = setInterval(() => {
            if (textNum.value >= tempBearText.length) {
                bearTime && clearInterval(bearTime)
                bearTalkContent.value = ''
                return
            }
            tempBearText[textNum.value].showRule()
            bearTalkContent.value = tempBearText[textNum.value].text
            bearTalkBtnText.value = tempBearText[textNum.value].btnText
            bearTalkBtnFun.value = tempBearText[textNum.value].btnFun
            textNum.value++
            if (textNum.value === tempBearText.length) showBearTalk.value = true
        }, 5000)
        tempBearText[textNum.value].showRule()
        bearTalkContent.value = tempBearText[textNum.value].text
        bearTalkBtnText.value = tempBearText[textNum.value].btnText
        bearTalkBtnFun.value = tempBearText[textNum.value].btnFun
        textNum.value++
        if (textNum.value >= tempBearText.length) {
            bearTime && clearInterval(bearTime)
            bearTalkContent.value = ''
        }
    }
    startTimer()
}

let uatLink = {
    home: `openurl-modal://https://*************/activity/myCity/#/myHome?lang=${
        useLang().lang.value
    }&forceHideNavigationBar=true`,
    home2: 'openurl-modal://https://<<cmcchkhsh_host>>/activity/myCity/#/newMyHome?lang=<<cmcchkhsh_cmplang>>&hideNavigationBar=true',
    home3: `openhkhshlogin://cmcchkhsh://metaverse?url=myhome`,
    sport: `cmcchkhsh://sgs_run`,
    grow: `openurl-modal://https://*************/activity/member/#/mPoints?hideNavigationBar=true&lang=${
        useLang().lang.value
    }`,
    city: `openurl-modal://https://*************/activity/myCity/#/cityMap?lang=${
        useLang().lang.value
    }&forceHideNavigationBar=true`,
    city2: `openhkhshlogin://cmcchkhsh://metaverse?url=mycity`
}
let patLink = {
    home: `openurl-modal://https://mylink.komect.com/activity/myCity/#/myHome?lang=${
        useLang().lang.value
    }&forceHideNavigationBar=true`,
    home2: 'openurl-modal://https://<<cmcchkhsh_host>>/activity/myCity/#/newMyHome?lang=<<cmcchkhsh_cmplang>>&hideNavigationBar=true',
    home3: `openhkhshlogin://cmcchkhsh://metaverse?url=myhome`,
    sport: `cmcchkhsh://sgs_run`,
    grow: `openurl-modal://https://mylink.komect.com/activity/member/#/mPoints?hideNavigationBar=true&lang=${
        useLang().lang.value
    }`,
    city: `openurl-modal://https://mylink.komect.com/activity/myCity/#/cityMap?lang=${
        useLang().lang.value
    }&forceHideNavigationBar=true`
}
// dialog.get('updateDialog').show({}, {maskClose:false})
// 跳转外链
const toLink = (name: string) => {
    // if (name == 'grow' && (!(useUserStore().isVip) || isSlash.value || !friendStore.isOwn)) {
    //     return
    // }
    if (name == 'grow') {
        logEventStatistics('garden_ip_click')
        if (!useUserStore().isVip || isSlash.value || !friendStore.isOwn) {
            return
        }
    }
    if (location.href.indexOf('friendId') !== -1) {
        return
    }
    const version = getAppVersion()
    const [first, seconds] = version.split('.')
    if (name == 'home' || name == 'grow') {
        // if (Number(first) < 9 || (Number(first) == 9 && Number(seconds) < 5)) {
        if (Number(first) < 11 && useUserStore().isHK == 0) {
            dialog.get('updateDialog').show({}, { maskClose: false })
            return
        }
    }
    switch (name) {
        case 'home':
            //點擊「我的家」按鈕人數
            useUserStore().isHK == 0 && logEventStatistics('garden_myhome_icon_click')
            break
        case 'city':
            //點擊「城市」按鈕人數
            logEventStatistics('garden_mycity_icon_click')
            break
        case 'sport':
            //點擊「我的運動」按鈕人數
            logEventStatistics('garden_wewalk_icon_click')
            break
        case 'grow':
            //點擊「我的成長值」按鈕人數
            logEventStatistics('garden_member_icon_click')
            break
    }
    if (name == 'home' && useUserStore().isHK == 0) {
        const ver = Number(version.split('.').join(''))
        if (ver >= 910 && ver < 1000) {
            // let link = isPat ? patLink : uatLink
            // window.location.href = link['home2']
            return
        } else if (ver >= 1000) {
            let link = isPat ? patLink : uatLink
            window.location.href = link['home3']
            return
        }
    }
    if (name == 'home' && useUserStore().isHK == 1) return
    if (name == 'city') {
        const ver = Number(version.split('.').join(''))
        console.log(ver)
        if (ver >= 1000) {
            let link = isPat ? patLink : uatLink
            window.location.href = link['city2']
            return
        }
    }
    let link = isPat ? patLink : uatLink
    window.location.href = link[name]
}

// 判断版本是否显示myCity按钮
let versions = ref(0)
const isShowMyCity = () => {
    const version = getAppVersion()
    const [first, seconds] = version.split('.')
    versions.value = Number(first)
}
/**
 * 打开我的成就
 */
const clickShowSheet = () => {
    nftAchieveRedPoint.value = false
    //點擊查看成就人數
    logEventStatistics('Mygarden_achievements_page')
    // showSheet.value = true
    toRouter('/achievement')
}

//toDo
//打开我的好友
const clickShowSheet1 = () => {
    showActSheet.value = true
    logEventStatistics('garden_states_click')
}
//回到顶部
const goTop = () => {
    window.scrollTo(0, 0)
}

const showActive = () => {
    if (useUserStore().inLogin) {
        useUserStore().updateNotifiedActivity()
        no_day_login.value = false
    }
    let actArr = [34, 31, 30, 29, 28, 22, 20, 16, 14, 13, 12, 10, 7, 6, 4]
    // let actArr = [27,26,25,24,23,22,21, 20, 18, 16, 14, 13, 12, 10, 7, 6, 4]
    actArr = actArr.filter((value) => {
        let act = state.activityArr.find((v) => v.id === value)
        if (!act.downTime && !act.startTime) {
            return true
        } else if (!act.downTime && act.startTime) {
            return new Date().getTime() > new Date(act.startTime).getTime()
        } else if (act.downTime && !act.startTime) {
            return new Date().getTime() < new Date(act.downTime).getTime()
        } else if (act.downTime && act.startTime) {
            return (
                new Date().getTime() < new Date(act.downTime).getTime() &&
                new Date().getTime() > new Date(act.startTime).getTime()
            )
        }
    })
    dialog.get('actDialog').show({ list: actArr }, { maskClose: false })
}
// 点击成就的花
const toInfo = async (item) => {
    if (event && event.target instanceof HTMLElement && event.target?.className.includes('give')) {
        return
    }
    // 点击红点后今日不显示
    if (item.nft_exists === false && useUserStore().inLogin) {
        await nftStore.nftAchieveRedDot(item.tree_id) //发送请求今天点击红点的成就了
        init() //刷新信息
    }
    // 后端传铸造结果
    dialog.get('plantDialog').show(
        {
            treeCode: item.code,
            tree_id: item.tree_id,
            planted: item.completed,
            foundryed: item.nft_exists,
            nft_group_id: item.nft_group_id,
            nft_recycled: item.nft_recycled
        },
        { maskClose: false, maskBgColor: 'rgba(0, 0, 0, 0.4)' }
    )
    // dialog.get('plantDialog').show({treeCode: item.code, tree_id: item.tree_id, planted: item.completed}, {maskClose:false, maskBgColor: 'rgba(0, 0, 0, 0.4)'})
}
// 主页成就红点
const achieveRedDot = () => {
    achieve__dot.value = false
    treeList.value.trees.forEach((i) => {
        if (i.nft_build_notice === true) {
            achieve__dot.value = true
        }
    })
}
let bearPicTimer: NodeJS.Timeout | null = null
const bearAni = () => {
    if (bearPicTimer) return
    // let len = bearPicArr.length
    // let rand:number = parseInt(len * Math.random())
    bearPic.value = bearPicArr[bearNum.value].img
    bearPicTimer = setTimeout(() => {
        bearPicTimer && clearTimeout(bearPicTimer)
        bearPicTimer = null
        bearPic.value = 'bear.png'
        if (bearNum.value < bearPicArr.length - 1) {
            bearNum.value++
        } else {
            bearNum.value = 0
        }
    }, bearPicArr[bearNum.value].time)
}

// 打开mygame
const openGame = async (jumpGame = '') => {
    logEventStatistics('garden_mygame_click')
    if (first_game_home.value && first_game_home.value != '1') {
        useUserStore().setUpData('first_game_home', '1')
        first_game_home.value = '1'
    }
    let token = envConfig.RUN_ENV == 'develop' ? envConfig.DEFAULT_TOKEN : ''
    const userInfo = await getUserInfo()
    if (userInfo && userInfo.authorization) {
        token = userInfo.authorization
    }
    if (useEnvConfig().RUN_ENV == 'develop' || useEnvConfig().RUN_ENV == 'uat') {
        // location.href = `openhkhshlogin://https://uatoss.mylinkapp.hk/mygame/center/web-uat/index.html?thirdPartyToken=<<cmcchkhsh_thirdparty_token_channel=mUBJ5xHj7Y>>&lang=<<cmcchkhsh_cmplang>>`
        jumpMyGame(true, jumpGame)
    } else {
        // location.href = `openhkhshlogin://https://cdn.mylinkapp.hk/mygame/center/index.html?thirdPartyToken=<<cmcchkhsh_thirdparty_token_channel=bU9JU0HAZG>>&lang=<<cmcchkhsh_cmplang>>`
        jumpMyGame(false, jumpGame)
    }
}

const upLevelFun = (res) => {
    let energyadd = res.user_actions.filter((item) => item.action_key == 'energy_add')
    let upgrade = res.user_actions
        .filter((item) => item.action_key == 'tree_upgrade')
        .sort((a, b) => {
            return a.tree_value.level - b.tree_value.level
        })
    let complete = res.user_actions.filter((item) => item.action_key == 'tree_complete')
    let action = [...upgrade, ...complete]
    if (action.length == 0) {
        energyadd.forEach((item) => {
            if (item.action_key == 'energy_add') {
                tree_energy.value += item.energy_value
                tree_energy1.value += item.energy_value
                user_energy.value += item.energy_value
                // energyCount.value -= 1  //浇花时也会减碳球
                handle()
            }
        })
    } else {
        action.forEach((item) => {
            if (item.action_key == 'tree_upgrade' || item.action_key == 'tree_complete') {
                upLevelCount += 1
                let reward_items = item.tree_value.reward_items
                let integral_value = item.integral_value || 0
                // 升级后 赋值风控状态
                let integralStatus = item.integral_status
                addBuffer(
                    item.tree_value.level - 1,
                    item.tree_value.level,
                    reward_items,
                    integral_value,
                    integralStatus
                )
            }
        })
    }
    eventBus.off('upLevelFun', upLevelFun)
    eventBus.once('upLevelFun', upLevelFun)
}

const clickBackToTop = () => {
    if (!isShowBackToTop.value) return
    window.scrollTo({
        top: 0,
        behavior: 'smooth'
    })
}

const scrollFunction = () => {
    if (document.body.scrollTop > window.innerHeight || document.documentElement.scrollTop > window.innerHeight) {
        const element = document.getElementById('back-to-top')
        if (element) {
            element.style.opacity = '1'
            isShowBackToTop.value = true
        }
    } else {
        const element = document.getElementById('back-to-top')
        if (element) {
            element.style.opacity = '0'
            isShowBackToTop.value = false
        }
    }
}

/** 成就红点 */
const nftAchieveRedPoint = ref(false)
/** 是否升级nft的提醒 */
let nftUpgradeFlag = false
/** 是否重铸的提醒 */
let nftRecastFlag = false
/** 打开nft相关提示提醒流程 */
const openNFTDialog = async () => {
    // 判断是否有植物完成但是未铸造的状态
    const treeList = useTreeStore().treeList.trees
    for (const tree of treeList) {
        // 如果存在种成但是未铸造的状态则显示重铸弹窗
        // TODO NFT
        if (tree.code == 'shuishirong' || tree.code == 'huangzhongmu') {
            continue
        }
        if (tree.completed && !tree.nft_exists) {
            nftRecastFlag = true
        }
        // 存在是否要升级的标记显示升级弹窗
        if (tree.nft_upgrade_notice) {
            const res = nftStore.nftEquity.equity_trees.find((nftTree) => nftTree.code === tree.code)
            if (res) {
                // 判断对应的权益是否生效
                nftUpgradeFlag = true
            }
        }
    }
    // 打开nft升级窗口的提醒
    openNFTUpgradeDialog()
}

/** 打开nft升级提示弹窗 */
const openNFTUpgradeDialog = async () => {
    if (!nftUpgradeFlag) {
        // 在不需要显示nft更新弹窗的情况下显示重新铸造的弹窗
        openReCasteDialog()
        return
    }

    // 只弹一次
    const info = useUserStore().userInfo
    const isFirst = info.webui_manage_values.first_nft_upgrade || 0

    // 是否已经出现过nft升级提示的弹窗
    if (isFirst === 1) {
        buyNft()
        return
    }

    useUserStore().setUpData('first_nft_upgrade', '1')
    useUserStore().userInfo.webui_manage_values.first_nft_upgrade = 1

    const nftDialog = dialog.get('upgradeNFTDialog')
    nftDialog.on('mask-close', async () => {
        // 点击空白关闭的情况下,显示成就红点
        nftAchieveRedPoint.value = true
        buyNft()
    })
    nftDialog.show({}, {})
}

/** 打开重新铸造弹窗 */
const openReCasteDialog = async () => {
    if (!nftRecastFlag) {
        buyNft()
        return
    }

    // 只弹一次
    const info = useUserStore().userInfo
    const isFirst = info.webui_manage_values.first_nft_recaste || 0

    // 是否已经出现过nft升级提示的弹窗
    if (isFirst === 1) {
        buyNft()
        return
    }

    useUserStore().setUpData('first_nft_recaste', '1')
    useUserStore().userInfo.webui_manage_values.first_nft_recaste = 1

    const redialog = dialog.get('reCasteDialog')
    redialog.on('mask-close', async () => {
        // 点击空白关闭的情况下,显示成就红点
        nftAchieveRedPoint.value = true
        buyNft()
    })
    redialog.show({}, {})
}

/**
 *
 */
const buyNft = async () => {
    const nftEquityRes = await taskStore.getNFTEquityState()
    // NFT購買權通知
    if (nftEquityRes.newest_cards.length > 0) {
        let buyNftDialog = dialog.get('buyNftDialog')
        buyNftDialog.beforeClose(linkDigFn as unknown as () => Promise<boolean>)
        buyNftDialog.show({ newest_cards: nftEquityRes.newest_cards }, { maskClose: true })
    } else {
        linkDigFn()
    }
}

// 注入变量至新手引导组件
provide('Guide', {
    virtualBub,
    showSheet,
    finLoad,
    stepIndex,
    level,
    guide_completed,
    buildTree,
    no_first_login,
    notified_first_logic1,
    notified_first_logic2,
    notified_first_logic3,
    notified_first_logic4,
    tree_energy,
    demand,
    judgePopUpNotification,
    ifLotArr,
    openNFTDialog
})
</script>

<style lang="less" scoped>
@import './home.scss';
</style>
